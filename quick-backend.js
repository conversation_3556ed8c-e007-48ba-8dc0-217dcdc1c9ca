/**
 * Quick RoadPulse Backend Server
 * Minimal backend for immediate testing
 */

const express = require('express');
const app = express();
const PORT = 3001;

// Simple in-memory database
let users = [];
let issues = [];
let sosAlerts = [];

// Middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

app.use(express.json());

// Helper functions
const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2);
const generateToken = (userId) => `token_${userId}_${Date.now()}`;

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'RoadPulse Backend is running',
    timestamp: new Date().toISOString(),
    database: 'In-Memory',
    version: '1.0.0'
  });
});

// API Documentation
app.get('/api/docs', (req, res) => {
  res.json({
    name: 'RoadPulse API',
    version: '1.0.0',
    description: 'Quick backend for RoadPulse testing',
    endpoints: {
      'GET /health': 'Health check',
      'POST /v1/auth/register': 'Register new user',
      'POST /v1/auth/login': 'Login user',
      'POST /v1/auth/google/register': 'Register with Google',
      'POST /v1/auth/google/login': 'Login with Google',
      'GET /v1/auth/profile': 'Get user profile',
      'GET /v1/issues': 'Get road issues',
      'POST /v1/issues': 'Create road issue',
      'GET /v1/sos/active': 'Get active SOS alerts',
      'POST /v1/sos': 'Create SOS alert'
    }
  });
});

// Email registration
app.post('/v1/auth/register', (req, res) => {
  try {
    const { email, password, name, phone } = req.body;

    // Check if user exists
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'An account with this email already exists'
      });
    }

    // Create user
    const userId = generateId();
    const user = {
      id: userId,
      email,
      password, // In production, this would be hashed
      name,
      phone,
      trustScore: 50,
      rating: 0.0,
      isVerified: false,
      provider: 'email',
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString()
    };

    users.push(user);

    // Generate token
    const token = generateToken(userId);

    console.log(`✅ User registered: ${email}`);

    res.status(201).json({
      message: 'User registered successfully',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        phone: user.phone,
        trustScore: user.trustScore,
        rating: user.rating,
        isVerified: user.isVerified,
        provider: user.provider,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      error: 'Registration failed',
      message: 'An error occurred during registration'
    });
  }
});

// Email login
app.post('/v1/auth/login', (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = users.find(u => u.email === email && u.password === password);
    if (!user) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Email or password is incorrect'
      });
    }

    // Update last login
    user.lastLoginAt = new Date().toISOString();

    // Generate token
    const token = generateToken(user.id);

    console.log(`✅ User logged in: ${email}`);

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        phone: user.phone,
        trustScore: user.trustScore,
        rating: user.rating,
        isVerified: user.isVerified,
        provider: user.provider,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Login failed',
      message: 'An error occurred during login'
    });
  }
});

// Google OAuth registration (simplified)
app.post('/v1/auth/google/register', (req, res) => {
  try {
    const { user_data } = req.body;

    // Check if user exists
    const existingUser = users.find(u => u.email === user_data.email);
    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'An account with this email already exists'
      });
    }

    // Create user
    const userId = generateId();
    const user = {
      id: userId,
      email: user_data.email,
      name: user_data.name,
      firstName: user_data.firstName,
      lastName: user_data.lastName,
      profileImage: user_data.profileImage,
      trustScore: 60,
      rating: 0.0,
      isVerified: true,
      provider: 'google',
      providerId: user_data.providerId,
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString()
    };

    users.push(user);

    // Generate token
    const token = generateToken(userId);

    console.log(`✅ Google user registered: ${user_data.email}`);

    res.status(201).json({
      message: 'Google registration successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        firstName: user.firstName,
        lastName: user.lastName,
        profileImage: user.profileImage,
        trustScore: user.trustScore,
        rating: user.rating,
        isVerified: user.isVerified,
        provider: user.provider,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    });
  } catch (error) {
    console.error('Google registration error:', error);
    res.status(500).json({
      error: 'Google registration failed',
      message: 'An error occurred during Google registration'
    });
  }
});

// Google OAuth login (simplified)
app.post('/v1/auth/google/login', (req, res) => {
  try {
    const { email } = req.body;

    // Find user
    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'No account found with this Google account'
      });
    }

    // Update last login
    user.lastLoginAt = new Date().toISOString();

    // Generate token
    const token = generateToken(user.id);

    console.log(`✅ Google user logged in: ${email}`);

    res.json({
      message: 'Google login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        firstName: user.firstName,
        lastName: user.lastName,
        profileImage: user.profileImage,
        trustScore: user.trustScore,
        rating: user.rating,
        isVerified: user.isVerified,
        provider: user.provider,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    });
  } catch (error) {
    console.error('Google login error:', error);
    res.status(500).json({
      error: 'Google login failed',
      message: 'An error occurred during Google login'
    });
  }
});

// Get user profile
app.get('/v1/auth/profile', (req, res) => {
  // Simple auth check
  const authHeader = req.headers['authorization'];
  if (!authHeader) {
    return res.status(401).json({ error: 'Authorization required' });
  }

  const token = authHeader.replace('Bearer ', '');
  const userId = token.split('_')[1]; // Extract user ID from token

  const user = users.find(u => u.id === userId);
  if (!user) {
    return res.status(404).json({
      error: 'User not found',
      message: 'User profile not found'
    });
  }

  res.json({
    id: user.id,
    email: user.email,
    name: user.name,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    profileImage: user.profileImage,
    trustScore: user.trustScore,
    rating: user.rating,
    isVerified: user.isVerified,
    provider: user.provider,
    createdAt: user.createdAt,
    lastLoginAt: user.lastLoginAt
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `The route ${req.originalUrl} does not exist`,
    suggestion: 'Check /api/docs for available endpoints'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 RoadPulse Backend running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 API docs: http://localhost:${PORT}/api/docs`);
  console.log(`🗄️ Database: In-Memory (for testing)`);
  console.log(`🔐 Authentication: Simplified (for testing)`);
  console.log(`✅ Ready for frontend connections!`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});
