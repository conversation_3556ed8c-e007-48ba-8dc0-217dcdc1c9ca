/**
 * Create Road Issues Table Migration
 */

exports.up = function(knex) {
  return knex.schema.createTable('road_issues', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('(lower(hex(randomblob(4))) || "-" || lower(hex(randomblob(2))) || "-4" || substr(lower(hex(randomblob(2))),2) || "-" || substr("89ab",abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || "-" || lower(hex(randomblob(6))))'));
    
    // Issue classification
    table.string('type').notNullable(); // 'pothole', 'construction', 'accident', 'traffic', 'police', 'camera', 'roadblock'
    table.string('category').nullable(); // Additional categorization
    table.string('severity').notNullable(); // 'low', 'medium', 'high', 'critical'
    table.string('status').defaultTo('open'); // 'open', 'in_progress', 'resolved', 'closed', 'duplicate'
    
    // Location information
    table.string('location_name').notNullable();
    table.decimal('latitude', 10, 8).notNullable();
    table.decimal('longitude', 11, 8).notNullable();
    table.string('address').nullable();
    table.string('road_name').nullable();
    table.string('district').nullable();
    table.string('city').nullable();
    table.string('country').defaultTo('Zimbabwe');
    
    // Issue details
    table.text('description').notNullable();
    table.text('resolution_notes').nullable();
    table.json('metadata').nullable(); // Additional structured data
    
    // Media attachments
    table.json('images').nullable(); // Array of image URLs
    table.json('videos').nullable(); // Array of video URLs
    table.string('primary_image_url').nullable();
    
    // Reporting and verification
    table.uuid('reported_by').references('id').inTable('users').onDelete('SET NULL');
    table.string('reporter_ip').nullable();
    table.boolean('verified').defaultTo(false);
    table.integer('verification_count').defaultTo(0);
    table.integer('dispute_count').defaultTo(0);
    table.json('verifications').nullable(); // Array of verification records
    
    // Impact and priority
    table.integer('impact_score').defaultTo(0); // Calculated impact score
    table.integer('priority').defaultTo(3); // 1 (highest) to 5 (lowest)
    table.integer('view_count').defaultTo(0);
    table.integer('report_count').defaultTo(1); // Number of times reported
    
    // Resolution tracking
    table.uuid('assigned_to').references('id').inTable('users').onDelete('SET NULL');
    table.timestamp('resolved_at').nullable();
    table.uuid('resolved_by').references('id').inTable('users').onDelete('SET NULL');
    table.decimal('resolution_cost', 10, 2).nullable();
    
    // Timestamps
    table.timestamps(true, true);
    table.timestamp('last_updated_at').defaultTo(knex.fn.now());
    
    // Indexes for performance
    table.index(['type']);
    table.index(['severity']);
    table.index(['status']);
    table.index(['latitude', 'longitude']);
    table.index(['created_at']);
    table.index(['reported_by']);
    table.index(['verified']);
    table.index(['city', 'country']);
    table.index(['priority', 'status']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('road_issues');
};
