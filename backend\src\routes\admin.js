/**
 * Admin Routes
 * Administrative functions
 */

const express = require('express');
const { requireAdmin } = require('../middleware/auth');
const { db } = require('../config/database');
const logger = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// All admin routes require admin privileges
router.use(requireAdmin);

/**
 * GET /admin/stats
 * Get system statistics
 */
router.get('/stats', asyncHandler(async (req, res) => {
  const stats = {
    users: {
      total: await db('users').count('* as count').first(),
      active: await db('users').where('is_active', true).count('* as count').first(),
      verified: await db('users').where('is_verified', true).count('* as count').first()
    },
    issues: {
      total: await db('road_issues').count('* as count').first(),
      open: await db('road_issues').where('status', 'open').count('* as count').first(),
      resolved: await db('road_issues').where('status', 'resolved').count('* as count').first()
    },
    sos: {
      total: await db('sos_alerts').count('* as count').first(),
      active: await db('sos_alerts').where('status', 'active').count('* as count').first()
    }
  };

  res.json(stats);
}));

module.exports = router;
