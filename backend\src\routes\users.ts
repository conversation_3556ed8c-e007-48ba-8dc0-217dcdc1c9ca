/**
 * Users Routes for RoadPulse Backend
 */

import express from 'express';
import { body, validationResult } from 'express-validator';
import { db } from '../database/connection';
import { authMiddleware } from '../middleware/auth';
import { rateLimiter } from '../middleware/rateLimiter';
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler';

const router = express.Router();

/**
 * GET /users/profile
 * Get current user profile
 */
router.get('/profile', [
  rateLimiter,
  authMiddleware
], asyncHandler(async (req, res) => {
  const user = await db('users')
    .select([
      'id', 'email', 'name', 'first_name', 'last_name', 'phone',
      'profile_image_url', 'trust_score', 'rating', 'is_verified',
      'provider', 'created_at', 'last_login_at'
    ])
    .where('id', req.userId)
    .first();

  if (!user) {
    return res.status(404).json({
      error: 'User not found',
      message: 'User profile not found'
    });
  }

  res.json({
    id: user.id,
    email: user.email,
    name: user.name,
    firstName: user.first_name,
    lastName: user.last_name,
    phone: user.phone,
    profileImage: user.profile_image_url,
    trustScore: user.trust_score,
    rating: user.rating,
    isVerified: user.is_verified,
    provider: user.provider,
    createdAt: user.created_at,
    lastLoginAt: user.last_login_at
  });
}));

/**
 * PUT /users/profile
 * Update user profile
 */
router.put('/profile', [
  rateLimiter,
  authMiddleware,
  body('name').optional().trim().isLength({ min: 2, max: 100 }),
  body('phone').optional().isMobilePhone('any'),
  body('first_name').optional().trim().isLength({ min: 1, max: 50 }),
  body('last_name').optional().trim().isLength({ min: 1, max: 50 })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const updates: any = {};
  const { name, phone, first_name, last_name } = req.body;

  if (name) updates.name = name;
  if (phone) updates.phone = phone;
  if (first_name) updates.first_name = first_name;
  if (last_name) updates.last_name = last_name;

  if (Object.keys(updates).length === 0) {
    return res.status(400).json({
      error: 'No updates provided',
      message: 'At least one field must be provided for update'
    });
  }

  await db('users').where('id', req.userId).update(updates);

  const updatedUser = await db('users')
    .select([
      'id', 'email', 'name', 'first_name', 'last_name', 'phone',
      'profile_image_url', 'trust_score', 'rating', 'is_verified',
      'provider', 'created_at', 'last_login_at'
    ])
    .where('id', req.userId)
    .first();

  res.json({
    message: 'Profile updated successfully',
    user: {
      id: updatedUser.id,
      email: updatedUser.email,
      name: updatedUser.name,
      firstName: updatedUser.first_name,
      lastName: updatedUser.last_name,
      phone: updatedUser.phone,
      profileImage: updatedUser.profile_image_url,
      trustScore: updatedUser.trust_score,
      rating: updatedUser.rating,
      isVerified: updatedUser.is_verified,
      provider: updatedUser.provider,
      createdAt: updatedUser.created_at,
      lastLoginAt: updatedUser.last_login_at
    }
  });
}));

export default router;
