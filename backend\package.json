{"name": "roadpulse-backend", "version": "1.0.0", "description": "RoadPulse Backend API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "build": "tsc", "test": "jest", "migrate": "knex migrate:latest", "seed": "knex seed:run", "db:setup": "knex migrate:latest && knex seed:run", "docker:build": "docker build -t roadpulse-backend .", "docker:run": "docker run -p 3001:3001 roadpulse-backend"}, "dependencies": {"bcrypt": "^5.1.0", "better-sqlite3": "^11.10.0", "cloudinary": "^1.40.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^7.2.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "knex": "^2.5.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "nodemailer": "^6.9.4", "pg": "^8.11.3", "rate-limiter-flexible": "^2.4.2", "redis": "^4.6.7", "socket.io": "^4.7.2", "twilio": "^4.15.0", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "@types/node-cron": "^3.0.8", "@types/nodemailer": "^6.4.9", "@types/pg": "^8.10.2", "@types/supertest": "^2.0.12", "jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "keywords": ["roadpulse", "zimbabwe", "roads", "sos", "navigation", "api"], "author": "RoadPulse Team", "license": "MIT"}