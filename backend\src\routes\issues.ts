/**
 * Road Issues Routes for RoadPulse Backend
 */

import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { db } from '../database/connection';
import { authMiddleware, optionalAuthMiddleware } from '../middleware/auth';
import { rateLimiter } from '../middleware/rateLimiter';
import { asyncHandler } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * GET /issues
 * Get road issues with optional filters
 */
router.get('/', [
  rateLimiter,
  optionalAuthMiddleware,
  query('type').optional().isIn(['pothole', 'construction', 'accident', 'traffic', 'police', 'camera', 'sos']),
  query('severity').optional().isIn(['low', 'medium', 'high', 'critical']),
  query('status').optional().isIn(['open', 'in_progress', 'resolved', 'closed']),
  query('lat').optional().isFloat(),
  query('lng').optional().isFloat(),
  query('radius').optional().isFloat({ min: 0.1, max: 50 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('offset').optional().isInt({ min: 0 })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const {
    type,
    severity,
    status = 'open',
    lat,
    lng,
    radius = 10,
    limit = 50,
    offset = 0
  } = req.query;

  let query = db('road_issues')
    .select([
      'id', 'type', 'location_name', 'latitude', 'longitude',
      'severity', 'description', 'image_url', 'status',
      'verified', 'verification_count', 'created_at'
    ])
    .limit(Number(limit))
    .offset(Number(offset))
    .orderBy('created_at', 'desc');

  // Apply filters
  if (type) query = query.where('type', type);
  if (severity) query = query.where('severity', severity);
  if (status) query = query.where('status', status);

  // Geographic filtering
  if (lat && lng) {
    const latNum = Number(lat);
    const lngNum = Number(lng);
    const radiusNum = Number(radius);
    
    // Use Haversine formula for distance calculation
    query = query.whereRaw(`
      (6371 * acos(
        cos(radians(?)) * cos(radians(latitude)) * 
        cos(radians(longitude) - radians(?)) + 
        sin(radians(?)) * sin(radians(latitude))
      )) <= ?
    `, [latNum, lngNum, latNum, radiusNum]);
  }

  const issues = await query;

  res.json({
    issues,
    pagination: {
      limit: Number(limit),
      offset: Number(offset),
      total: issues.length
    }
  });
}));

/**
 * POST /issues
 * Create a new road issue
 */
router.post('/', [
  rateLimiter,
  authMiddleware,
  body('type').isIn(['pothole', 'construction', 'accident', 'traffic', 'police', 'camera', 'sos']),
  body('location_name').trim().isLength({ min: 3, max: 255 }),
  body('latitude').isFloat({ min: -90, max: 90 }),
  body('longitude').isFloat({ min: -180, max: 180 }),
  body('severity').isIn(['low', 'medium', 'high', 'critical']),
  body('description').trim().isLength({ min: 10, max: 1000 }),
  body('image_url').optional().isURL()
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const {
    type,
    location_name,
    latitude,
    longitude,
    severity,
    description,
    image_url
  } = req.body;

  const [issueId] = await db('road_issues').insert({
    type,
    location_name,
    latitude,
    longitude,
    severity,
    description,
    image_url,
    reported_by: req.userId,
    status: 'open',
    verified: false,
    verification_count: 0
  }).returning('id');

  const issue = await db('road_issues')
    .select('*')
    .where('id', issueId)
    .first();

  logger.info(`New issue created: ${issueId} by user ${req.userId}`);

  res.status(201).json({
    message: 'Issue created successfully',
    issue
  });
}));

/**
 * GET /issues/:id
 * Get specific issue details
 */
router.get('/:id', [
  rateLimiter,
  optionalAuthMiddleware
], asyncHandler(async (req, res) => {
  const { id } = req.params;

  const issue = await db('road_issues')
    .select('*')
    .where('id', id)
    .first();

  if (!issue) {
    return res.status(404).json({
      error: 'Issue not found',
      message: 'The requested issue does not exist'
    });
  }

  res.json({ issue });
}));

/**
 * PUT /issues/:id/verify
 * Verify or dispute an issue
 */
router.put('/:id/verify', [
  rateLimiter,
  authMiddleware,
  body('verification').isIn(['confirm', 'dispute']),
  body('notes').optional().trim().isLength({ max: 500 })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { id } = req.params;
  const { verification, notes } = req.body;

  const issue = await db('road_issues')
    .select('*')
    .where('id', id)
    .first();

  if (!issue) {
    return res.status(404).json({
      error: 'Issue not found',
      message: 'The requested issue does not exist'
    });
  }

  // Update verification count and status
  const updates: any = {
    verification_count: issue.verification_count + 1
  };

  if (verification === 'confirm') {
    updates.verified = true;
  }

  await db('road_issues')
    .where('id', id)
    .update(updates);

  logger.info(`Issue ${id} ${verification}ed by user ${req.userId}`);

  res.json({
    message: `Issue ${verification}ed successfully`,
    verification: {
      type: verification,
      notes,
      verified_by: req.userId,
      timestamp: new Date()
    }
  });
}));

/**
 * PUT /issues/:id/status
 * Update issue status (admin only for now)
 */
router.put('/:id/status', [
  rateLimiter,
  authMiddleware,
  body('status').isIn(['open', 'in_progress', 'resolved', 'closed']),
  body('notes').optional().trim().isLength({ max: 500 })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { id } = req.params;
  const { status, notes } = req.body;

  const issue = await db('road_issues')
    .select('*')
    .where('id', id)
    .first();

  if (!issue) {
    return res.status(404).json({
      error: 'Issue not found',
      message: 'The requested issue does not exist'
    });
  }

  await db('road_issues')
    .where('id', id)
    .update({ status });

  logger.info(`Issue ${id} status updated to ${status} by user ${req.userId}`);

  res.json({
    message: 'Issue status updated successfully',
    status: {
      new_status: status,
      notes,
      updated_by: req.userId,
      timestamp: new Date()
    }
  });
}));

export default router;
