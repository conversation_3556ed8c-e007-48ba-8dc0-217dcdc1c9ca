/**
 * Ultra Simple RoadPulse Backend Server
 * Using only Node.js built-in modules
 */

const http = require('http');
const url = require('url');

const PORT = 3001;

// Simple in-memory database
let users = [];
let issues = [];
let sosAlerts = [];

// Helper functions
const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2);
const generateToken = (userId) => `token_${userId}_${Date.now()}`;

// CORS headers
const setCORSHeaders = (res) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
};

// JSON response helper
const sendJSON = (res, statusCode, data) => {
  setCORSHeaders(res);
  res.writeHead(statusCode, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(data));
};

// Parse JSON body
const parseBody = (req) => {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
};

// Create HTTP server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    setCORSHeaders(res);
    res.writeHead(200);
    res.end();
    return;
  }

  try {
    // Health check
    if (path === '/health' && method === 'GET') {
      sendJSON(res, 200, {
        status: 'ok',
        message: 'RoadPulse Backend is running',
        timestamp: new Date().toISOString(),
        database: 'In-Memory',
        version: '1.0.0'
      });
      return;
    }

    // API Documentation
    if (path === '/api/docs' && method === 'GET') {
      sendJSON(res, 200, {
        name: 'RoadPulse API',
        version: '1.0.0',
        description: 'Ultra simple backend for RoadPulse testing',
        endpoints: {
          'GET /health': 'Health check',
          'POST /v1/auth/register': 'Register new user',
          'POST /v1/auth/login': 'Login user',
          'POST /v1/auth/google/register': 'Register with Google',
          'POST /v1/auth/google/login': 'Login with Google',
          'GET /v1/auth/profile': 'Get user profile'
        }
      });
      return;
    }

    // Email registration
    if (path === '/v1/auth/register' && method === 'POST') {
      const body = await parseBody(req);
      const { email, password, name, phone } = body;

      // Check if user exists
      const existingUser = users.find(u => u.email === email);
      if (existingUser) {
        sendJSON(res, 409, {
          error: 'User already exists',
          message: 'An account with this email already exists'
        });
        return;
      }

      // Create user
      const userId = generateId();
      const user = {
        id: userId,
        email,
        password,
        name,
        phone,
        trustScore: 50,
        rating: 0.0,
        isVerified: false,
        provider: 'email',
        createdAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString()
      };

      users.push(user);

      // Generate token
      const token = generateToken(userId);

      console.log(`✅ User registered: ${email}`);

      sendJSON(res, 201, {
        message: 'User registered successfully',
        token,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          phone: user.phone,
          trustScore: user.trustScore,
          rating: user.rating,
          isVerified: user.isVerified,
          provider: user.provider,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt
        }
      });
      return;
    }

    // Email login
    if (path === '/v1/auth/login' && method === 'POST') {
      const body = await parseBody(req);
      const { email, password } = body;

      // Find user
      const user = users.find(u => u.email === email && u.password === password);
      if (!user) {
        sendJSON(res, 401, {
          error: 'Invalid credentials',
          message: 'Email or password is incorrect'
        });
        return;
      }

      // Update last login
      user.lastLoginAt = new Date().toISOString();

      // Generate token
      const token = generateToken(user.id);

      console.log(`✅ User logged in: ${email}`);

      sendJSON(res, 200, {
        message: 'Login successful',
        token,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          phone: user.phone,
          trustScore: user.trustScore,
          rating: user.rating,
          isVerified: user.isVerified,
          provider: user.provider,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt
        }
      });
      return;
    }

    // Google OAuth registration (simplified)
    if (path === '/v1/auth/google/register' && method === 'POST') {
      const body = await parseBody(req);
      const { user_data } = body;

      // Check if user exists
      const existingUser = users.find(u => u.email === user_data.email);
      if (existingUser) {
        sendJSON(res, 409, {
          error: 'User already exists',
          message: 'An account with this email already exists'
        });
        return;
      }

      // Create user
      const userId = generateId();
      const user = {
        id: userId,
        email: user_data.email,
        name: user_data.name,
        firstName: user_data.firstName,
        lastName: user_data.lastName,
        profileImage: user_data.profileImage,
        trustScore: 60,
        rating: 0.0,
        isVerified: true,
        provider: 'google',
        providerId: user_data.providerId,
        createdAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString()
      };

      users.push(user);

      // Generate token
      const token = generateToken(userId);

      console.log(`✅ Google user registered: ${user_data.email}`);

      sendJSON(res, 201, {
        message: 'Google registration successful',
        token,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          firstName: user.firstName,
          lastName: user.lastName,
          profileImage: user.profileImage,
          trustScore: user.trustScore,
          rating: user.rating,
          isVerified: user.isVerified,
          provider: user.provider,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt
        }
      });
      return;
    }

    // Google OAuth login (simplified)
    if (path === '/v1/auth/google/login' && method === 'POST') {
      const body = await parseBody(req);
      const { email } = body;

      // Find user
      const user = users.find(u => u.email === email);
      if (!user) {
        sendJSON(res, 404, {
          error: 'User not found',
          message: 'No account found with this Google account'
        });
        return;
      }

      // Update last login
      user.lastLoginAt = new Date().toISOString();

      // Generate token
      const token = generateToken(user.id);

      console.log(`✅ Google user logged in: ${email}`);

      sendJSON(res, 200, {
        message: 'Google login successful',
        token,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          firstName: user.firstName,
          lastName: user.lastName,
          profileImage: user.profileImage,
          trustScore: user.trustScore,
          rating: user.rating,
          isVerified: user.isVerified,
          provider: user.provider,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt
        }
      });
      return;
    }

    // Get user profile
    if (path === '/v1/auth/profile' && method === 'GET') {
      const authHeader = req.headers['authorization'];
      if (!authHeader) {
        sendJSON(res, 401, { error: 'Authorization required' });
        return;
      }

      const token = authHeader.replace('Bearer ', '');
      const userId = token.split('_')[1]; // Extract user ID from token

      const user = users.find(u => u.id === userId);
      if (!user) {
        sendJSON(res, 404, {
          error: 'User not found',
          message: 'User profile not found'
        });
        return;
      }

      sendJSON(res, 200, {
        id: user.id,
        email: user.email,
        name: user.name,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        profileImage: user.profileImage,
        trustScore: user.trustScore,
        rating: user.rating,
        isVerified: user.isVerified,
        provider: user.provider,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      });
      return;
    }

    // 404 handler
    sendJSON(res, 404, {
      error: 'Route not found',
      message: `The route ${path} does not exist`,
      suggestion: 'Check /api/docs for available endpoints'
    });

  } catch (error) {
    console.error('Server error:', error);
    sendJSON(res, 500, {
      error: 'Internal server error',
      message: error.message
    });
  }
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 RoadPulse Backend running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 API docs: http://localhost:${PORT}/api/docs`);
  console.log(`🗄️ Database: In-Memory (for testing)`);
  console.log(`🔐 Authentication: Simplified (for testing)`);
  console.log(`✅ Ready for frontend connections!`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    process.exit(0);
  });
});
