/**
 * Authentication Routes for RoadPulse Backend
 * Handles email and Google OAuth authentication
 */

import express from 'express';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { OAuth2Client } from 'google-auth-library';
import { body, validationResult } from 'express-validator';
import { db } from '../database/connection';
import { authMiddleware } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = express.Router();

// Google OAuth client
const googleClient = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

// JWT configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
const REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || '7d';

/**
 * Generate JWT tokens
 */
const generateTokens = (userId: string) => {
  const accessToken = jwt.sign({ userId }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
  const refreshToken = jwt.sign({ userId, type: 'refresh' }, JWT_SECRET, { expiresIn: REFRESH_TOKEN_EXPIRES_IN });
  
  return { accessToken, refreshToken };
};

/**
 * Get user profile data
 */
const getUserProfile = async (userId: string) => {
  const user = await db('users')
    .select([
      'id', 'email', 'name', 'first_name', 'last_name', 'phone', 
      'profile_image_url', 'trust_score', 'rating', 'is_verified',
      'provider', 'provider_id', 'created_at', 'last_login_at'
    ])
    .where('id', userId)
    .first();

  if (!user) {
    throw new Error('User not found');
  }

  return {
    id: user.id,
    email: user.email,
    name: user.name,
    firstName: user.first_name,
    lastName: user.last_name,
    phone: user.phone,
    profileImage: user.profile_image_url,
    trustScore: user.trust_score,
    rating: user.rating,
    isVerified: user.is_verified,
    provider: user.provider,
    providerId: user.provider_id,
    createdAt: user.created_at,
    lastLoginAt: user.last_login_at
  };
};

/**
 * POST /auth/register
 * Register new user with email and password
 */
router.post('/register', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }),
  body('name').trim().isLength({ min: 2 }),
  body('phone').optional().isMobilePhone('any')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { email, password, name, phone } = req.body;

    // Check if user already exists
    const existingUser = await db('users').where('email', email).first();
    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'An account with this email already exists'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Split name into first and last name
    const nameParts = name.trim().split(' ');
    const firstName = nameParts[0];
    const lastName = nameParts.slice(1).join(' ') || '';

    // Create user
    const [userId] = await db('users').insert({
      email,
      password: hashedPassword,
      name,
      first_name: firstName,
      last_name: lastName,
      phone,
      provider: 'email',
      trust_score: 50,
      rating: 0.0,
      is_verified: false,
      created_at: new Date(),
      last_login_at: new Date()
    }).returning('id');

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(userId);

    // Get user profile
    const userProfile = await getUserProfile(userId);

    logger.info(`New user registered: ${email}`);

    res.status(201).json({
      message: 'User registered successfully',
      token: accessToken,
      refresh_token: refreshToken,
      user: userProfile
    });
  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      error: 'Registration failed',
      message: 'An error occurred during registration'
    });
  }
});

/**
 * POST /auth/login
 * Login with email and password
 */
router.post('/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').notEmpty()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { email, password } = req.body;

    // Find user
    const user = await db('users').where('email', email).first();
    if (!user) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Email or password is incorrect'
      });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Email or password is incorrect'
      });
    }

    // Update last login
    await db('users').where('id', user.id).update({
      last_login_at: new Date()
    });

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Get user profile
    const userProfile = await getUserProfile(user.id);

    logger.info(`User logged in: ${email}`);

    res.json({
      message: 'Login successful',
      token: accessToken,
      refresh_token: refreshToken,
      user: userProfile
    });
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      error: 'Login failed',
      message: 'An error occurred during login'
    });
  }
});

/**
 * POST /auth/google/login
 * Login with existing Google account
 */
router.post('/google/login', [
  body('id_token').notEmpty(),
  body('google_id').notEmpty(),
  body('email').isEmail().normalizeEmail()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { id_token, google_id, email } = req.body;

    // Verify Google ID token
    const ticket = await googleClient.verifyIdToken({
      idToken: id_token,
      audience: process.env.GOOGLE_CLIENT_ID
    });

    const payload = ticket.getPayload();
    if (!payload || payload.sub !== google_id) {
      return res.status(401).json({
        error: 'Invalid Google token',
        message: 'Google authentication failed'
      });
    }

    // Find user by Google ID or email
    const user = await db('users')
      .where('provider_id', google_id)
      .orWhere('email', email)
      .first();

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'No account found with this Google account'
      });
    }

    // Update last login
    await db('users').where('id', user.id).update({
      last_login_at: new Date()
    });

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Get user profile
    const userProfile = await getUserProfile(user.id);

    logger.info(`Google user logged in: ${email}`);

    res.json({
      message: 'Google login successful',
      token: accessToken,
      refresh_token: refreshToken,
      user: userProfile
    });
  } catch (error) {
    logger.error('Google login error:', error);
    res.status(500).json({
      error: 'Google login failed',
      message: 'An error occurred during Google login'
    });
  }
});

/**
 * POST /auth/google/register
 * Register new user with Google account
 */
router.post('/google/register', [
  body('id_token').notEmpty(),
  body('user_data.email').isEmail().normalizeEmail(),
  body('user_data.name').trim().isLength({ min: 2 }),
  body('user_data.firstName').trim().isLength({ min: 1 }),
  body('user_data.lastName').trim().isLength({ min: 1 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { id_token, user_data } = req.body;

    // Verify Google ID token
    const ticket = await googleClient.verifyIdToken({
      idToken: id_token,
      audience: process.env.GOOGLE_CLIENT_ID
    });

    const payload = ticket.getPayload();
    if (!payload) {
      return res.status(401).json({
        error: 'Invalid Google token',
        message: 'Google authentication failed'
      });
    }

    // Check if user already exists
    const existingUser = await db('users')
      .where('email', user_data.email)
      .orWhere('provider_id', user_data.providerId)
      .first();

    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'An account with this email or Google account already exists'
      });
    }

    // Create user
    const [userId] = await db('users').insert({
      email: user_data.email,
      name: user_data.name,
      first_name: user_data.firstName,
      last_name: user_data.lastName,
      profile_image_url: user_data.profileImage,
      provider: 'google',
      provider_id: user_data.providerId,
      trust_score: 50,
      rating: 0.0,
      is_verified: true, // Google accounts are pre-verified
      created_at: new Date(),
      last_login_at: new Date()
    }).returning('id');

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(userId);

    // Get user profile
    const userProfile = await getUserProfile(userId);

    logger.info(`New Google user registered: ${user_data.email}`);

    res.status(201).json({
      message: 'Google registration successful',
      token: accessToken,
      refresh_token: refreshToken,
      user: userProfile
    });
  } catch (error) {
    logger.error('Google registration error:', error);
    res.status(500).json({
      error: 'Google registration failed',
      message: 'An error occurred during Google registration'
    });
  }
});

/**
 * POST /auth/refresh
 * Refresh access token
 */
router.post('/refresh', [
  body('refresh_token').notEmpty()
], async (req, res) => {
  try {
    const { refresh_token } = req.body;

    // Verify refresh token
    const decoded = jwt.verify(refresh_token, JWT_SECRET) as any;
    
    if (decoded.type !== 'refresh') {
      return res.status(401).json({
        error: 'Invalid token',
        message: 'Invalid refresh token'
      });
    }

    // Generate new access token
    const accessToken = jwt.sign({ userId: decoded.userId }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

    res.json({
      token: accessToken
    });
  } catch (error) {
    logger.error('Token refresh error:', error);
    res.status(401).json({
      error: 'Token refresh failed',
      message: 'Invalid or expired refresh token'
    });
  }
});

/**
 * GET /auth/profile
 * Get current user profile
 */
router.get('/profile', authMiddleware, async (req, res) => {
  try {
    const userProfile = await getUserProfile(req.userId);
    res.json(userProfile);
  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({
      error: 'Failed to get profile',
      message: 'An error occurred while fetching profile'
    });
  }
});

/**
 * PUT /auth/profile
 * Update user profile
 */
router.put('/profile', authMiddleware, [
  body('name').optional().trim().isLength({ min: 2 }),
  body('phone').optional().isMobilePhone('any'),
  body('firstName').optional().trim().isLength({ min: 1 }),
  body('lastName').optional().trim().isLength({ min: 1 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const updates: any = {};
    const { name, phone, firstName, lastName } = req.body;

    if (name) updates.name = name;
    if (phone) updates.phone = phone;
    if (firstName) updates.first_name = firstName;
    if (lastName) updates.last_name = lastName;

    if (Object.keys(updates).length === 0) {
      return res.status(400).json({
        error: 'No updates provided',
        message: 'At least one field must be provided for update'
      });
    }

    updates.updated_at = new Date();

    await db('users').where('id', req.userId).update(updates);

    const userProfile = await getUserProfile(req.userId);

    logger.info(`User profile updated: ${req.userId}`);

    res.json({
      message: 'Profile updated successfully',
      user: userProfile
    });
  } catch (error) {
    logger.error('Update profile error:', error);
    res.status(500).json({
      error: 'Failed to update profile',
      message: 'An error occurred while updating profile'
    });
  }
});

/**
 * POST /auth/logout
 * Logout user (client-side token removal)
 */
router.post('/logout', authMiddleware, (req, res) => {
  // In a more sophisticated setup, you might want to blacklist the token
  logger.info(`User logged out: ${req.userId}`);
  
  res.json({
    message: 'Logout successful'
  });
});

export default router;
