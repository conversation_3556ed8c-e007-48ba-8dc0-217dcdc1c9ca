# RoadPulse Environment Configuration

# API Configuration
VITE_API_URL=http://localhost:3001/v1
VITE_WS_URL=ws://localhost:3001

# Development Settings
VITE_USE_MOCK_DATA=false
VITE_ENABLE_REALTIME=true
VITE_DEBUG_MODE=false

# Map Configuration
VITE_MAPBOX_TOKEN=your_mapbox_token_here
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Google OAuth Configuration
VITE_GOOGLE_CLIENT_ID=your_google_oauth_client_id_here
VITE_GOOGLE_REDIRECT_URI=http://localhost:8081/auth/callback

# External Services
VITE_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
VITE_SENTRY_DSN=your_sentry_dsn_here

# Firebase Configuration (for push notifications)
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# Analytics
VITE_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PUSH_NOTIFICATIONS=true
VITE_ENABLE_OFFLINE_MODE=true

# Development URLs (for local development)
# VITE_API_URL=http://localhost:3001/v1
# VITE_WS_URL=ws://localhost:3001
# VITE_USE_MOCK_DATA=true
