/**
 * Simple Express Server for Testing
 * Basic health check and CORS setup
 */

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: 'http://localhost:8080',
  credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'RoadPulse Backend is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Basic API endpoints for testing
app.get('/v1/test', (req, res) => {
  res.json({
    message: 'RoadPulse API is working!',
    timestamp: new Date().toISOString()
  });
});

// Mock authentication endpoint
app.post('/v1/auth/google/login', (req, res) => {
  console.log('Google login attempt:', req.body);
  res.json({
    message: 'Google login endpoint reached',
    token: 'mock-jwt-token',
    user: {
      id: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Test User'
    }
  });
});

// Mock authentication endpoint
app.post('/v1/auth/google/register', (req, res) => {
  console.log('Google register attempt:', req.body);
  res.json({
    message: 'Google registration endpoint reached',
    token: 'mock-jwt-token',
    user: {
      id: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Test User'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `The route ${req.originalUrl} does not exist`,
    availableRoutes: [
      'GET /health',
      'GET /v1/test',
      'POST /v1/auth/google/login',
      'POST /v1/auth/google/register'
    ]
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 RoadPulse Backend running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/v1/test`);
  console.log(`🔐 Auth endpoints available for Google OAuth testing`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});
