/**
 * Authentication Routes
 * Handles user registration, login, and OAuth
 */

const express = require('express');
const bcrypt = require('bcrypt');
const { body, validationResult } = require('express-validator');
const { OAuth2Client } = require('google-auth-library');
const { v4: uuidv4 } = require('uuid');

const { db } = require('../config/database');
const config = require('../config/environment');
const logger = require('../utils/logger');
const { 
  generateAccessToken, 
  generateRefreshToken, 
  verifyToken,
  authenticateToken,
  authRateLimit 
} = require('../middleware/auth');

const router = express.Router();

// Initialize Google OAuth client
const googleClient = new OAuth2Client(config.GOOGLE_CLIENT_ID);

/**
 * Helper function to format user response
 */
const formatUserResponse = (user) => ({
  id: user.id,
  email: user.email,
  name: user.name,
  firstName: user.first_name,
  lastName: user.last_name,
  phone: user.phone,
  profileImage: user.profile_image_url,
  trustScore: user.trust_score,
  rating: parseFloat(user.rating) || 0.0,
  isVerified: Boolean(user.is_verified),
  provider: user.provider,
  location: {
    country: user.country,
    city: user.city
  },
  preferences: user.preferences ? JSON.parse(user.preferences) : {
    notifications: true,
    locationSharing: true,
    emergencyContacts: []
  },
  createdAt: user.created_at,
  lastLoginAt: user.last_login_at
});

/**
 * POST /auth/register
 * Register new user with email and password
 */
router.post('/register', [
  authRateLimit,
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
  body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be 2-100 characters'),
  body('phone').optional().isMobilePhone('any').withMessage('Valid phone number required'),
  body('firstName').optional().trim().isLength({ min: 1, max: 50 }),
  body('lastName').optional().trim().isLength({ min: 1, max: 50 })
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Please check your input data',
        details: errors.array()
      });
    }

    const { email, password, name, phone, firstName, lastName } = req.body;

    // Check if user already exists
    const existingUser = await db('users')
      .select('id', 'email')
      .where('email', email)
      .first();

    if (existingUser) {
      logger.logSecurity('Registration attempt with existing email', {
        email,
        ip: req.ip
      });
      
      return res.status(409).json({
        error: 'User already exists',
        message: 'An account with this email already exists',
        code: 'EMAIL_EXISTS'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, config.BCRYPT_ROUNDS);

    // Parse name if firstName/lastName not provided
    let fName = firstName;
    let lName = lastName;
    
    if (!fName && !lName && name) {
      const nameParts = name.trim().split(' ');
      fName = nameParts[0];
      lName = nameParts.slice(1).join(' ') || '';
    }

    // Create user
    const userId = uuidv4();
    const userData = {
      id: userId,
      email,
      password: hashedPassword,
      name: name.trim(),
      first_name: fName,
      last_name: lName,
      phone,
      provider: 'email',
      country: 'Zimbabwe', // Default for RoadPulse
      is_verified: false,
      is_active: true,
      trust_score: 50,
      rating: 0.0,
      preferences: JSON.stringify({
        notifications: true,
        locationSharing: true,
        emergencyContacts: []
      }),
      created_at: new Date().toISOString(),
      last_login_at: new Date().toISOString()
    };

    await db('users').insert(userData);

    // Generate tokens
    const accessToken = generateAccessToken(userId);
    const refreshToken = generateRefreshToken(userId);

    // Get created user
    const user = await db('users')
      .select('*')
      .where('id', userId)
      .first();

    logger.logBusinessEvent('User registered', {
      userId,
      email,
      provider: 'email',
      ip: req.ip
    });

    res.status(201).json({
      message: 'User registered successfully',
      token: accessToken,
      refreshToken,
      user: formatUserResponse(user)
    });

  } catch (error) {
    logger.logError(error, req);
    res.status(500).json({
      error: 'Registration failed',
      message: 'An error occurred during registration',
      code: 'REGISTRATION_ERROR'
    });
  }
});

/**
 * POST /auth/login
 * Login with email and password
 */
router.post('/login', [
  authRateLimit,
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password').notEmpty().withMessage('Password is required')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Please check your input data',
        details: errors.array()
      });
    }

    const { email, password } = req.body;

    // Find user
    const user = await db('users')
      .select('*')
      .where('email', email)
      .first();

    if (!user) {
      logger.logSecurity('Login attempt with non-existent email', {
        email,
        ip: req.ip
      });
      
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Email or password is incorrect',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Check if account is active
    if (!user.is_active) {
      logger.logSecurity('Login attempt with inactive account', {
        userId: user.id,
        email,
        ip: req.ip
      });
      
      return res.status(401).json({
        error: 'Account disabled',
        message: 'Your account has been disabled',
        code: 'ACCOUNT_DISABLED'
      });
    }

    // Check if account is locked
    if (user.locked_until && new Date(user.locked_until) > new Date()) {
      return res.status(401).json({
        error: 'Account locked',
        message: 'Your account is temporarily locked due to multiple failed login attempts',
        code: 'ACCOUNT_LOCKED',
        lockedUntil: user.locked_until
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    
    if (!isValidPassword) {
      // Increment failed login attempts
      const failedAttempts = (user.failed_login_attempts || 0) + 1;
      const updateData = { failed_login_attempts: failedAttempts };
      
      // Lock account after 5 failed attempts
      if (failedAttempts >= 5) {
        updateData.locked_until = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
        logger.logSecurity('Account locked due to failed attempts', {
          userId: user.id,
          email,
          failedAttempts,
          ip: req.ip
        });
      }
      
      await db('users').where('id', user.id).update(updateData);
      
      logger.logSecurity('Failed login attempt', {
        userId: user.id,
        email,
        failedAttempts,
        ip: req.ip
      });
      
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Email or password is incorrect',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Reset failed login attempts and update last login
    await db('users')
      .where('id', user.id)
      .update({
        failed_login_attempts: 0,
        locked_until: null,
        last_login_at: new Date().toISOString(),
        last_login_ip: req.ip
      });

    // Generate tokens
    const accessToken = generateAccessToken(user.id);
    const refreshToken = generateRefreshToken(user.id);

    logger.logBusinessEvent('User logged in', {
      userId: user.id,
      email,
      provider: 'email',
      ip: req.ip
    });

    res.json({
      message: 'Login successful',
      token: accessToken,
      refreshToken,
      user: formatUserResponse(user)
    });

  } catch (error) {
    logger.logError(error, req);
    res.status(500).json({
      error: 'Login failed',
      message: 'An error occurred during login',
      code: 'LOGIN_ERROR'
    });
  }
});

/**
 * POST /auth/google/register
 * Register with Google OAuth
 */
router.post('/google/register', [
  authRateLimit,
  body('id_token').notEmpty().withMessage('Google ID token is required'),
  body('user_data').isObject().withMessage('User data is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { id_token, user_data } = req.body;

    // Verify Google ID token
    const ticket = await googleClient.verifyIdToken({
      idToken: id_token,
      audience: config.GOOGLE_CLIENT_ID
    });

    const payload = ticket.getPayload();
    if (!payload) {
      return res.status(401).json({
        error: 'Invalid Google token',
        message: 'Google authentication failed'
      });
    }

    // Check if user already exists
    const existingUser = await db('users')
      .select('id', 'email')
      .where('email', user_data.email)
      .orWhere('provider_id', user_data.providerId)
      .first();

    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'An account with this email or Google account already exists'
      });
    }

    // Create user
    const userId = uuidv4();
    const userData = {
      id: userId,
      email: user_data.email,
      name: user_data.name,
      first_name: user_data.firstName,
      last_name: user_data.lastName,
      profile_image_url: user_data.profileImage,
      provider: 'google',
      provider_id: user_data.providerId,
      provider_data: JSON.stringify(payload),
      country: 'Zimbabwe',
      is_verified: true, // Google accounts are pre-verified
      is_active: true,
      trust_score: 60, // Slightly higher for verified accounts
      rating: 0.0,
      preferences: JSON.stringify({
        notifications: true,
        locationSharing: true,
        emergencyContacts: []
      }),
      created_at: new Date().toISOString(),
      last_login_at: new Date().toISOString()
    };

    await db('users').insert(userData);

    // Generate tokens
    const accessToken = generateAccessToken(userId);
    const refreshToken = generateRefreshToken(userId);

    // Get created user
    const user = await db('users')
      .select('*')
      .where('id', userId)
      .first();

    logger.logBusinessEvent('User registered via Google', {
      userId,
      email: user_data.email,
      provider: 'google',
      ip: req.ip
    });

    res.status(201).json({
      message: 'Google registration successful',
      token: accessToken,
      refreshToken,
      user: formatUserResponse(user)
    });

  } catch (error) {
    logger.logError(error, req);
    res.status(500).json({
      error: 'Google registration failed',
      message: 'An error occurred during Google registration'
    });
  }
});

/**
 * POST /auth/google/login
 * Login with Google OAuth
 */
router.post('/google/login', [
  authRateLimit,
  body('id_token').notEmpty().withMessage('Google ID token is required'),
  body('google_id').notEmpty().withMessage('Google ID is required'),
  body('email').isEmail().withMessage('Valid email is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { id_token, google_id, email } = req.body;

    // Verify Google ID token
    const ticket = await googleClient.verifyIdToken({
      idToken: id_token,
      audience: config.GOOGLE_CLIENT_ID
    });

    const payload = ticket.getPayload();
    if (!payload || payload.sub !== google_id) {
      return res.status(401).json({
        error: 'Invalid Google token',
        message: 'Google authentication failed'
      });
    }

    // Find user
    const user = await db('users')
      .select('*')
      .where('provider_id', google_id)
      .orWhere('email', email)
      .first();

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'No account found with this Google account'
      });
    }

    // Check if account is active
    if (!user.is_active) {
      return res.status(401).json({
        error: 'Account disabled',
        message: 'Your account has been disabled'
      });
    }

    // Update last login
    await db('users')
      .where('id', user.id)
      .update({
        last_login_at: new Date().toISOString(),
        last_login_ip: req.ip
      });

    // Generate tokens
    const accessToken = generateAccessToken(user.id);
    const refreshToken = generateRefreshToken(user.id);

    logger.logBusinessEvent('User logged in via Google', {
      userId: user.id,
      email,
      provider: 'google',
      ip: req.ip
    });

    res.json({
      message: 'Google login successful',
      token: accessToken,
      refreshToken,
      user: formatUserResponse(user)
    });

  } catch (error) {
    logger.logError(error, req);
    res.status(500).json({
      error: 'Google login failed',
      message: 'An error occurred during Google login'
    });
  }
});

/**
 * GET /auth/profile
 * Get current user profile
 */
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = await db('users')
      .select('*')
      .where('id', req.userId)
      .first();

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User profile not found'
      });
    }

    res.json(formatUserResponse(user));

  } catch (error) {
    logger.logError(error, req);
    res.status(500).json({
      error: 'Failed to get profile',
      message: 'An error occurred while fetching profile'
    });
  }
});

/**
 * POST /auth/refresh
 * Refresh access token
 */
router.post('/refresh', [
  body('refreshToken').notEmpty().withMessage('Refresh token is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { refreshToken } = req.body;

    // Verify refresh token
    const decoded = verifyToken(refreshToken, 'refresh');

    // Check if user exists and is active
    const user = await db('users')
      .select('id', 'is_active')
      .where('id', decoded.userId)
      .first();

    if (!user || !user.is_active) {
      return res.status(401).json({
        error: 'Invalid refresh token',
        message: 'User not found or inactive'
      });
    }

    // Generate new access token
    const newAccessToken = generateAccessToken(user.id);

    res.json({
      message: 'Token refreshed successfully',
      token: newAccessToken
    });

  } catch (error) {
    logger.logError(error, req);
    res.status(401).json({
      error: 'Token refresh failed',
      message: 'Invalid or expired refresh token'
    });
  }
});

/**
 * POST /auth/logout
 * Logout user (client-side token removal)
 */
router.post('/logout', authenticateToken, (req, res) => {
  logger.logBusinessEvent('User logged out', {
    userId: req.userId,
    ip: req.ip
  });

  res.json({
    message: 'Logout successful'
  });
});

module.exports = router;
