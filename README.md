# SmartRoadPulse - Interactive Road Monitoring System

A comprehensive road monitoring and reporting system with real-time interactive mapping capabilities.

## 🗺️ Interactive Map Features

### Real-time Road Issue Visualization
- **OpenStreetMap Integration**: High-quality, free mapping with global coverage
- **Descriptive Issue Icons**: Custom SVG icons that visually represent each problem type:
  - 🕳️ **Potholes**: Jagged hole representation with depth indicators
  - 🚧 **Construction**: Traffic cone with safety stripes
  - 🌊 **Flooding**: Water waves with depth visualization
  - ⚠️ **Accidents**: Warning triangle with impact indicators
  - 👮 **Police Locations**: Shield badge with star for law enforcement presence
  - 📹 **Camera Locations**: Security camera with lens for traffic monitoring
- **Color-coded Severity Levels**:
  - 🔴 Critical (Red)
  - 🟠 High (Orange)
  - 🟡 Medium (Yellow)
  - 🟢 Low (Green)
- **Detailed Popups**: Issue information, reporter details, timestamps, and status
- **User Location Detection**: GPS integration with location centering
- **Enhanced Legend**: Visual guide showing both icon types and severity colors
- **Mobile-Responsive**: Optimized for all device sizes

### Navigation & Access
- **Home** (`/`) - African-themed landing page with cultural elements
- **Dashboard** (`/dashboard`) - Authority interface with embedded map
- **Mobile** (`/mobile`) - Citizen-focused mobile interface
- **Full Map** (`/map`) - Dedicated full-screen map experience with filtering
- **Route Planning** (`/route-planning`) - AI-powered route planning with real-time problem detection
- **Profile** (`/profile`) - Comprehensive user management with Zimbabwean context
- **Interactive Elements**: Click markers for details, location button for GPS centering

### 🛣️ Smart Route Planning & Navigation
- **AI-Powered Route Calculation**: Advanced routing algorithms using Leaflet Routing Machine
- **Real-time Problem Detection**: Automatically identifies issues along your planned route
- **Interactive Destination Search**: Search and select from popular Zimbabwe destinations
- **Route Safety Analysis**:
  - Safety score calculation (0-100%) based on route issues
  - Distance and estimated travel time
  - Issue count and severity breakdown
- **Visual Route Display**:
  - Clear route visualization with start/end markers
  - Color-coded route lines (Zimbabwe gold and white)
  - Issue markers along the route path
- **Comprehensive Route Information**:
  - Detailed list of problems along the route
  - Issue severity indicators (critical, high, medium, low)
  - Problem descriptions and locations
  - Timestamps for when issues were reported
- **Zimbabwe-Specific Destinations**: Pre-loaded popular locations including:
  - Harare International Airport
  - University of Zimbabwe
  - Major shopping centers (Eastgate, Borrowdale Village, Sam Levy's)
  - Residential areas (Chitungwiza, Epworth)
- **Multi-Tab Interface**:
  - **Plan Route**: Interactive route planning with destination search
  - **Route Analysis**: Detailed breakdown of route safety and issues
  - **Alternative Routes**: Future feature for route alternatives
- **Mobile-Responsive**: Optimized for both desktop and mobile route planning

### Law Enforcement & Traffic Monitoring
- **Police Location Reporting**: Citizens can report and view police presence including:
  - Traffic checkpoints and speed enforcement
  - Mobile patrol units
  - Document verification roadblocks
  - Emergency response locations
- **Camera Location Tracking**: Comprehensive camera monitoring system showing:
  - Fixed speed enforcement cameras
  - Traffic intersection monitoring
  - Mobile speed camera units
  - Security surveillance cameras
- **Real-time Updates**: Last seen timestamps for active police and camera locations
- **Type Classification**: Detailed categorization of police activities and camera functions

### Sample Data
Includes realistic sample data for Harare, Zimbabwe with 17 different items including road issues, police locations, and camera positions across various locations, severity levels, and reporting sources.

## Project info

**URL**: https://lovable.dev/projects/d2afc8dc-eb1d-4b98-9721-08a2466d94a1

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/d2afc8dc-eb1d-4b98-9721-08a2466d94a1) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- **Vite** - Fast build tool and development server
- **TypeScript** - Type-safe JavaScript development
- **React 18** - Modern React with hooks and concurrent features
- **React Leaflet** - Interactive maps for React applications
- **Leaflet** - Leading open-source JavaScript library for mobile-friendly interactive maps
- **Leaflet Routing Machine** - Advanced routing and navigation capabilities
- **OpenStreetMap** - Free, editable map data
- **shadcn-ui** - Beautiful, accessible UI components
- **Tailwind CSS** - Utility-first CSS framework with African-themed color palette
- **React Router DOM** - Declarative routing for React

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/d2afc8dc-eb1d-4b98-9721-08a2466d94a1) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
