/**
 * RoadPulse Production Backend Server
 * Enterprise-grade Node.js API server with comprehensive features
 */

const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const { createServer } = require('http');
const { Server: SocketIOServer } = require('socket.io');

// Import configuration and utilities
const config = require('./config/environment');
const { db, testConnection, initializeDatabase, closeDatabase } = require('./config/database');
const logger = require('./utils/logger');

// Import middleware
const authMiddleware = require('./middleware/auth');
const errorHandler = require('./middleware/errorHandler');
const validationMiddleware = require('./middleware/validation');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const issueRoutes = require('./routes/issues');
const sosRoutes = require('./routes/sos');
const uploadRoutes = require('./routes/upload');
const adminRoutes = require('./routes/admin');

// Import services
const websocketService = require('./services/websocket');
const notificationService = require('./services/notification');

// Validate configuration
config.validateConfig();

// Initialize Express app
const app = express();
const server = createServer(app);

// Initialize WebSocket server
const io = new SocketIOServer(server, {
  cors: {
    origin: config.FRONTEND_URLS,
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "wss:", "ws:"]
    }
  },
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    if (config.FRONTEND_URLS.includes(origin)) {
      return callback(null, true);
    }
    
    // In development, allow localhost with any port
    if (config.isDevelopment() && origin.includes('localhost')) {
      return callback(null, true);
    }
    
    callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Compression middleware
app.use(compression());

// Request logging
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim())
  }
}));

// Body parsing middleware
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ 
  extended: true, 
  limit: '10mb' 
}));

// Rate limiting
if (config.ENABLE_RATE_LIMITING) {
  const limiter = rateLimit({
    windowMs: config.RATE_LIMIT_WINDOW_MS,
    max: config.RATE_LIMIT_MAX_REQUESTS,
    message: {
      error: 'Too many requests',
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: Math.ceil(config.RATE_LIMIT_WINDOW_MS / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      logger.warn(`Rate limit exceeded for IP: ${req.ip}`);
      res.status(429).json({
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: Math.ceil(config.RATE_LIMIT_WINDOW_MS / 1000)
      });
    }
  });
  
  app.use(limiter);
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
    database: 'connected',
    memory: process.memoryUsage(),
    features: {
      authentication: true,
      websockets: true,
      fileUpload: true,
      notifications: true,
      rateLimit: config.ENABLE_RATE_LIMITING
    }
  });
});

// API Documentation endpoint
app.get('/api/docs', (req, res) => {
  res.json({
    name: 'RoadPulse API',
    version: '1.0.0',
    description: 'Production-grade API for Zimbabwe road monitoring and emergency response',
    endpoints: {
      authentication: {
        'POST /v1/auth/register': 'Register new user with email',
        'POST /v1/auth/login': 'Login with email and password',
        'POST /v1/auth/google/login': 'Login with Google OAuth',
        'POST /v1/auth/google/register': 'Register with Google OAuth',
        'GET /v1/auth/profile': 'Get current user profile',
        'POST /v1/auth/refresh': 'Refresh access token',
        'POST /v1/auth/logout': 'Logout user'
      },
      users: {
        'GET /v1/users/profile': 'Get user profile',
        'PUT /v1/users/profile': 'Update user profile',
        'GET /v1/users/stats': 'Get user statistics'
      },
      issues: {
        'GET /v1/issues': 'Get road issues with filters',
        'POST /v1/issues': 'Report new road issue',
        'GET /v1/issues/:id': 'Get specific issue',
        'PUT /v1/issues/:id/verify': 'Verify or dispute issue',
        'PUT /v1/issues/:id/status': 'Update issue status'
      },
      sos: {
        'POST /v1/sos': 'Create SOS alert',
        'GET /v1/sos/active': 'Get active SOS alerts',
        'PUT /v1/sos/:id/respond': 'Respond to SOS alert',
        'PUT /v1/sos/:id/resolve': 'Resolve SOS alert'
      },
      upload: {
        'POST /v1/upload/image': 'Upload image file',
        'POST /v1/upload/video': 'Upload video file'
      }
    },
    websocket: {
      events: ['location:update', 'issue:new', 'sos:alert', 'sos:response']
    }
  });
});

// API Routes
app.use('/v1/auth', authRoutes);
app.use('/v1/users', authMiddleware, userRoutes);
app.use('/v1/issues', issueRoutes);
app.use('/v1/sos', authMiddleware, sosRoutes);
app.use('/v1/upload', authMiddleware, uploadRoutes);

// Admin routes (protected)
app.use('/v1/admin', authMiddleware, adminRoutes);

// Serve uploaded files
app.use('/uploads', express.static(config.UPLOAD_PATH));

// WebSocket setup
websocketService.initialize(io);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `The route ${req.method} ${req.originalUrl} does not exist`,
    suggestion: 'Check /api/docs for available endpoints'
  });
});

// Global error handler
app.use(errorHandler);

// Graceful shutdown handler
const gracefulShutdown = async (signal) => {
  logger.info(`${signal} received, starting graceful shutdown...`);
  
  // Stop accepting new connections
  server.close(async () => {
    logger.info('HTTP server closed');
    
    try {
      // Close database connections
      await closeDatabase();
      
      // Close other services
      await notificationService.close();
      
      logger.info('✅ Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      logger.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  });
  
  // Force shutdown after 30 seconds
  setTimeout(() => {
    logger.error('❌ Forced shutdown after timeout');
    process.exit(1);
  }, 30000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start server
const startServer = async () => {
  try {
    // Test database connection
    const dbConnected = await testConnection();
    if (!dbConnected) {
      throw new Error('Database connection failed');
    }
    
    // Initialize database schema
    await initializeDatabase();
    
    // Initialize notification service
    await notificationService.initialize();
    
    // Start HTTP server
    server.listen(config.PORT, config.HOST, () => {
      logger.info(`🚀 RoadPulse API server running on http://${config.HOST}:${config.PORT}`);
      logger.info(`📊 Health check: http://${config.HOST}:${config.PORT}/health`);
      logger.info(`📚 API docs: http://${config.HOST}:${config.PORT}/api/docs`);
      logger.info(`🌍 Environment: ${config.NODE_ENV}`);
      logger.info(`🔒 Security: Helmet, CORS, Rate Limiting`);
      logger.info(`🗄️ Database: ${config.DATABASE_URL ? 'PostgreSQL' : 'SQLite'}`);
      logger.info(`🔌 WebSocket: Enabled on same port`);
      logger.info(`✅ Server ready for connections!`);
    });
    
  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();

// Export for testing
module.exports = { app, server, io };
