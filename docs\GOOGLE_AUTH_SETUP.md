# 🔐 Google OAuth Authentication Setup Guide

## Overview
Complete guide to implement Google OAuth authentication for RoadPulse, including account creation and login functionality.

## 🚀 Quick Start

### 1. Google Cloud Console Setup

#### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Name it "RoadPulse" or similar

#### Step 2: Enable Google+ API
1. Navigate to **APIs & Services** > **Library**
2. Search for "Google+ API" 
3. Click **Enable**

#### Step 3: Create OAuth 2.0 Credentials
1. Go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Configure consent screen first if prompted:
   - **Application name**: SmartRoadPulse
   - **User support email**: Your email
   - **Developer contact**: Your email
   - **Authorized domains**: Add your domain (e.g., roadpulse.zw)

4. Create OAuth client:
   - **Application type**: Web application
   - **Name**: RoadPulse Web Client
   - **Authorized JavaScript origins**:
     - `http://localhost:3000` (development)
     - `https://yourdomain.com` (production)
   - **Authorized redirect URIs**:
     - `http://localhost:3000/auth/callback` (development)
     - `https://yourdomain.com/auth/callback` (production)

#### Step 4: Get Client ID
1. Copy the **Client ID** (starts with numbers, ends with `.apps.googleusercontent.com`)
2. Save the **Client Secret** for backend use

### 2. Frontend Configuration

#### Environment Variables
Add to your `.env.local` file:

```bash
# Google OAuth Configuration
VITE_GOOGLE_CLIENT_ID=your_client_id_here.apps.googleusercontent.com
VITE_GOOGLE_REDIRECT_URI=http://localhost:8081/auth/callback

# For production
# VITE_GOOGLE_CLIENT_ID=your_production_client_id.apps.googleusercontent.com
# VITE_GOOGLE_REDIRECT_URI=https://yourdomain.com/auth/callback
```

#### Install Dependencies
```bash
npm install google-auth-library
```

### 3. Backend Configuration

#### Environment Variables
Add to your `backend/.env` file:

```bash
# Google OAuth
GOOGLE_CLIENT_ID=your_client_id_here.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_client_secret_here

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_EXPIRES_IN=7d
```

#### Install Dependencies
```bash
cd backend
npm install google-auth-library express-validator bcrypt jsonwebtoken
```

### 4. Database Schema Updates

Add these columns to your users table:

```sql
-- Add Google OAuth columns to users table
ALTER TABLE users ADD COLUMN provider VARCHAR(20) DEFAULT 'email';
ALTER TABLE users ADD COLUMN provider_id VARCHAR(255);
ALTER TABLE users ADD COLUMN profile_image_url TEXT;
ALTER TABLE users ADD COLUMN first_name VARCHAR(100);
ALTER TABLE users ADD COLUMN last_name VARCHAR(100);

-- Add indexes for performance
CREATE INDEX idx_users_provider_id ON users(provider_id);
CREATE INDEX idx_users_email ON users(email);
```

## 🔧 Implementation Details

### Frontend Integration

#### 1. Wrap App with AuthProvider
```tsx
// src/App.tsx
import { AuthProvider } from './contexts/AuthContext';

function App() {
  return (
    <AuthProvider>
      {/* Your app components */}
    </AuthProvider>
  );
}
```

#### 2. Use Authentication in Components
```tsx
// Any component
import { useAuth } from '@/contexts/AuthContext';

const MyComponent = () => {
  const { user, isAuthenticated, signInWithGoogle, signOut } = useAuth();

  const handleGoogleSignIn = async () => {
    const result = await signInWithGoogle();
    if (result.success) {
      console.log('Signed in successfully!', result.isNewUser);
    }
  };

  return (
    <div>
      {isAuthenticated ? (
        <div>
          <p>Welcome, {user?.name}!</p>
          <button onClick={signOut}>Sign Out</button>
        </div>
      ) : (
        <button onClick={handleGoogleSignIn}>
          Sign in with Google
        </button>
      )}
    </div>
  );
};
```

### Backend Integration

#### 1. Authentication Middleware
```typescript
// backend/src/middleware/auth.ts
import jwt from 'jsonwebtoken';

export const authMiddleware = (req: any, res: any, next: any) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    return res.status(401).json({ error: 'No token provided' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    req.userId = decoded.userId;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
  }
};
```

#### 2. Database Connection
```typescript
// backend/src/database/connection.ts
import knex from 'knex';

export const db = knex({
  client: 'postgresql',
  connection: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'roadpulse'
  }
});
```

## 🧪 Testing the Integration

### 1. Test Google Sign-In Flow
1. Start your development servers:
   ```bash
   # Frontend
   npm start

   # Backend
   cd backend && npm run dev
   ```

2. Open `http://localhost:3000`
3. Click "Sign in with Google"
4. Complete Google OAuth flow
5. Check that user is created in database

### 2. Test API Endpoints
```bash
# Test registration
curl -X POST http://localhost:3001/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Test User"
  }'

# Test login
curl -X POST http://localhost:3001/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

## 🔒 Security Best Practices

### 1. Environment Security
- Never commit `.env` files to version control
- Use different Google OAuth clients for development/production
- Rotate JWT secrets regularly

### 2. Token Security
- Use HTTPS in production
- Implement token refresh mechanism
- Set appropriate token expiration times

### 3. User Data Protection
- Hash passwords with bcrypt (salt rounds ≥ 12)
- Validate all user inputs
- Implement rate limiting on auth endpoints

## 🚀 Production Deployment

### 1. Frontend (Vercel/Netlify)
```bash
# Environment variables to set:
VITE_GOOGLE_CLIENT_ID=your_production_client_id
VITE_API_URL=https://api.yourdomain.com/v1
VITE_GOOGLE_REDIRECT_URI=https://yourdomain.com/auth/callback
```

### 2. Backend (Railway/Heroku/DigitalOcean)
```bash
# Environment variables to set:
GOOGLE_CLIENT_ID=your_production_client_id
GOOGLE_CLIENT_SECRET=your_production_client_secret
JWT_SECRET=your_super_secure_production_secret
DATABASE_URL=postgresql://user:pass@host:port/db
FRONTEND_URL=https://yourdomain.com
```

### 3. Domain Configuration
1. Update Google OAuth settings with production domains
2. Configure CORS for production URLs
3. Set up SSL certificates

## 🐛 Troubleshooting

### Common Issues

#### "Invalid Client ID"
- Check that `REACT_APP_GOOGLE_CLIENT_ID` is set correctly
- Verify the client ID in Google Cloud Console
- Ensure the domain is authorized

#### "Redirect URI Mismatch"
- Check authorized redirect URIs in Google Cloud Console
- Ensure exact match including protocol (http/https)

#### "User Not Found" on Google Login
- This is expected for new users
- The system will automatically create an account

#### CORS Errors
- Check backend CORS configuration
- Ensure frontend URL is whitelisted

### Debug Mode
Enable debug logging:

```bash
# Frontend
VITE_DEBUG_MODE=true

# Backend
DEBUG=true
LOG_LEVEL=debug
```

## 📱 Mobile App Integration

For future mobile app development:

### React Native
```bash
npm install @react-native-google-signin/google-signin
```

### Flutter
```yaml
dependencies:
  google_sign_in: ^6.1.5
```

## 🎯 Next Steps

1. **Phone Verification**: Add SMS verification for enhanced security
2. **Social Logins**: Add Facebook, Twitter authentication
3. **Two-Factor Auth**: Implement 2FA for sensitive operations
4. **Account Recovery**: Email-based password reset
5. **Profile Management**: Enhanced user profile features

This setup provides a complete, production-ready authentication system for RoadPulse with Google OAuth integration!
