<!DOCTYPE html>
<html>
<head>
    <title>Route Testing</title>
    <script>
        // Test the realistic route generation
        function createRealisticRoute(start, end) {
            console.log('🛣️ Creating realistic road-following route');
            
            const latDiff = end[0] - start[0];
            const lngDiff = end[1] - start[1];
            
            const waypoints = [start];
            
            // Add intermediate points with visible road-like curves
            const distance = Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);
            const numWaypoints = Math.max(5, Math.floor(distance * 100));
            
            console.log(`Distance: ${distance}, Waypoints: ${numWaypoints}`);
            
            for (let i = 1; i < numWaypoints; i++) {
                const progress = i / numWaypoints;
                
                // Create more visible road curvature
                const roadCurve = Math.sin(progress * Math.PI * 4) * distance * 0.1;
                const streetOffset = Math.cos(progress * Math.PI * 6) * distance * 0.05;
                
                // Apply curves perpendicular to the main direction
                const perpLat = -lngDiff / distance;
                const perpLng = latDiff / distance;
                
                const lat = start[0] + (latDiff * progress) + (perpLat * roadCurve);
                const lng = start[1] + (lngDiff * progress) + (perpLng * roadCurve) + (streetOffset * 0.01);
                
                waypoints.push([lat, lng]);
            }
            
            waypoints.push(end);
            
            console.log('Generated waypoints:', waypoints);
            return waypoints;
        }
        
        function testRoute() {
            const start = [-17.8292, 31.0522]; // Harare CBD
            const end = [-17.8100, 31.0700];   // Borrowdale
            
            const waypoints = createRealisticRoute(start, end);
            
            console.log('Test Results:');
            console.log('Start:', start);
            console.log('End:', end);
            console.log('Waypoints generated:', waypoints.length);
            console.log('First few waypoints:', waypoints.slice(0, 5));
            
            // Check if waypoints are different from straight line
            const straightLine = [start, end];
            const hasVariation = waypoints.length > 2;
            
            console.log('Has variation from straight line:', hasVariation);
            
            if (hasVariation) {
                const midPoint = waypoints[Math.floor(waypoints.length / 2)];
                const straightMidLat = start[0] + (end[0] - start[0]) * 0.5;
                const straightMidLng = start[1] + (end[1] - start[1]) * 0.5;
                const deviation = Math.sqrt(
                    Math.pow(midPoint[0] - straightMidLat, 2) + 
                    Math.pow(midPoint[1] - straightMidLng, 2)
                );
                console.log('Deviation from straight line at midpoint:', deviation);
            }
        }
        
        // Run test when page loads
        window.onload = testRoute;
    </script>
</head>
<body>
    <h1>Route Testing</h1>
    <p>Check the browser console for test results.</p>
    <button onclick="testRoute()">Run Test Again</button>
</body>
</html>
