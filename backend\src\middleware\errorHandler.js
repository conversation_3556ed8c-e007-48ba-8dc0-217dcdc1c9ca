/**
 * Production Error Handler Middleware
 * Comprehensive error handling with logging and security
 */

const logger = require('../utils/logger');
const config = require('../config/environment');

/**
 * Global error handler middleware
 */
const errorHandler = (error, req, res, next) => {
  // Log the error
  logger.logError(error, req, {
    timestamp: new Date().toISOString(),
    requestId: req.id || 'unknown'
  });

  // Default error response
  let statusCode = error.statusCode || error.status || 500;
  let message = error.message || 'Internal server error';
  let code = error.code || 'INTERNAL_ERROR';
  let details = null;

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400;
    code = 'VALIDATION_ERROR';
    message = 'Validation failed';
    details = error.details || error.errors;
  } else if (error.name === 'UnauthorizedError' || error.name === 'JsonWebTokenError') {
    statusCode = 401;
    code = 'UNAUTHORIZED';
    message = 'Authentication failed';
  } else if (error.name === 'ForbiddenError') {
    statusCode = 403;
    code = 'FORBIDDEN';
    message = 'Access denied';
  } else if (error.name === 'NotFoundError') {
    statusCode = 404;
    code = 'NOT_FOUND';
    message = 'Resource not found';
  } else if (error.name === 'ConflictError') {
    statusCode = 409;
    code = 'CONFLICT';
    message = 'Resource conflict';
  } else if (error.name === 'TooManyRequestsError') {
    statusCode = 429;
    code = 'TOO_MANY_REQUESTS';
    message = 'Rate limit exceeded';
  }

  // Database errors
  if (error.code === 'SQLITE_CONSTRAINT_UNIQUE' || error.code === '23505') {
    statusCode = 409;
    code = 'DUPLICATE_RESOURCE';
    message = 'Resource already exists';
  } else if (error.code === 'SQLITE_CONSTRAINT_FOREIGNKEY' || error.code === '23503') {
    statusCode = 400;
    code = 'INVALID_REFERENCE';
    message = 'Invalid resource reference';
  } else if (error.message && error.message.includes('connection')) {
    statusCode = 503;
    code = 'DATABASE_ERROR';
    message = 'Database connection failed';
  }

  // Google OAuth errors
  if (error.message && error.message.includes('Google')) {
    statusCode = 401;
    code = 'OAUTH_ERROR';
    message = 'OAuth authentication failed';
  }

  // File upload errors
  if (error.code === 'LIMIT_FILE_SIZE') {
    statusCode = 413;
    code = 'FILE_TOO_LARGE';
    message = 'File size exceeds limit';
  } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    statusCode = 400;
    code = 'INVALID_FILE';
    message = 'Invalid file type or field name';
  }

  // Don't expose internal errors in production
  if (config.isProduction() && statusCode === 500) {
    message = 'Internal server error';
    details = null;
  }

  // Security: Don't expose stack traces in production
  const response = {
    error: code,
    message,
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method
  };

  // Add details in development or for client errors
  if (details && (config.isDevelopment() || statusCode < 500)) {
    response.details = details;
  }

  // Add stack trace in development
  if (config.isDevelopment() && error.stack) {
    response.stack = error.stack;
  }

  // Add request ID if available
  if (req.id) {
    response.requestId = req.id;
  }

  // Send error response
  res.status(statusCode).json(response);
};

/**
 * 404 handler for unmatched routes
 */
const notFoundHandler = (req, res) => {
  const error = {
    error: 'NOT_FOUND',
    message: `Route ${req.method} ${req.path} not found`,
    timestamp: new Date().toISOString(),
    suggestion: 'Check /api/docs for available endpoints'
  };

  logger.logSecurity('404 - Route not found', {
    method: req.method,
    path: req.path,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(404).json(error);
};

/**
 * Async error wrapper
 * Wraps async route handlers to catch errors automatically
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Create custom API error
 */
const createApiError = (message, statusCode = 500, code = null, details = null) => {
  const error = new Error(message);
  error.statusCode = statusCode;
  error.code = code;
  error.details = details;
  return error;
};

/**
 * Validation error handler
 */
const handleValidationError = (errors) => {
  const error = createApiError('Validation failed', 400, 'VALIDATION_ERROR');
  error.details = errors.array ? errors.array() : errors;
  return error;
};

module.exports = {
  errorHandler,
  notFoundHandler,
  asyncHandler,
  createApiError,
  handleValidationError
};
