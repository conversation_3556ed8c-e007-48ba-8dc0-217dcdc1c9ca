/**
 * SOS Routes
 * Emergency alert system
 */

const express = require('express');
const { validationSets } = require('../middleware/validation');
const { db } = require('../config/database');
const logger = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

/**
 * POST /sos
 * Create SOS alert
 */
router.post('/', [
  ...validationSets.createSOS
], asyncHandler(async (req, res) => {
  const {
    category,
    description,
    latitude,
    longitude,
    location_address
  } = req.body;

  const alertId = uuidv4();
  const alertData = {
    id: alertId,
    user_id: req.userId,
    category,
    description,
    latitude,
    longitude,
    location_address,
    status: 'active',
    risk_score: 0.3,
    fraud_probability: 0.1,
    country: 'Zimbabwe',
    created_at: new Date().toISOString()
  };

  await db('sos_alerts').insert(alertData);

  const alert = await db('sos_alerts')
    .select('*')
    .where('id', alertId)
    .first();

  logger.logBusinessEvent('SOS alert created', {
    alertId,
    category,
    userId: req.userId,
    location: { latitude, longitude }
  });

  res.status(201).json({
    message: 'SOS alert created successfully',
    alert
  });
}));

/**
 * GET /sos/active
 * Get active SOS alerts
 */
router.get('/active', [
  ...validationSets.locationQuery
], asyncHandler(async (req, res) => {
  const { lat, lng, radius = 10 } = req.query;

  let query = db('sos_alerts')
    .select('*')
    .where('status', 'active')
    .orderBy('created_at', 'desc');

  // Geographic filtering
  if (lat && lng) {
    const latNum = Number(lat);
    const lngNum = Number(lng);
    const radiusNum = Number(radius);
    
    query = query.whereRaw(`
      (6371 * acos(
        cos(radians(?)) * cos(radians(latitude)) * 
        cos(radians(longitude) - radians(?)) + 
        sin(radians(?)) * sin(radians(latitude))
      )) <= ?
    `, [latNum, lngNum, latNum, radiusNum]);
  }

  const alerts = await query;

  res.json({ alerts });
}));

module.exports = router;
