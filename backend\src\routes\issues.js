/**
 * Road Issues Routes
 * Handle road issue reporting and management
 */

const express = require('express');
const { validationSets } = require('../middleware/validation');
const { db } = require('../config/database');
const { optionalAuth } = require('../middleware/auth');
const logger = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

/**
 * GET /issues
 * Get road issues with optional filters
 */
router.get('/', [
  optionalAuth,
  ...validationSets.listWithPagination,
  ...validationSets.locationQuery
], asyncHandler(async (req, res) => {
  const {
    type,
    severity,
    status = 'open',
    lat,
    lng,
    radius = 10,
    limit = 50,
    offset = 0
  } = req.query;

  let query = db('road_issues')
    .select([
      'id', 'type', 'location_name', 'latitude', 'longitude',
      'severity', 'description', 'primary_image_url', 'status',
      'verified', 'verification_count', 'created_at'
    ])
    .limit(Number(limit))
    .offset(Number(offset))
    .orderBy('created_at', 'desc');

  // Apply filters
  if (type) query = query.where('type', type);
  if (severity) query = query.where('severity', severity);
  if (status) query = query.where('status', status);

  // Geographic filtering
  if (lat && lng) {
    const latNum = Number(lat);
    const lngNum = Number(lng);
    const radiusNum = Number(radius);
    
    // Simple distance calculation (for production, use PostGIS)
    query = query.whereRaw(`
      (6371 * acos(
        cos(radians(?)) * cos(radians(latitude)) * 
        cos(radians(longitude) - radians(?)) + 
        sin(radians(?)) * sin(radians(latitude))
      )) <= ?
    `, [latNum, lngNum, latNum, radiusNum]);
  }

  const issues = await query;

  res.json({
    issues,
    pagination: {
      limit: Number(limit),
      offset: Number(offset),
      total: issues.length
    }
  });
}));

/**
 * POST /issues
 * Create a new road issue
 */
router.post('/', [
  ...validationSets.createIssue
], asyncHandler(async (req, res) => {
  const {
    type,
    location_name,
    latitude,
    longitude,
    severity,
    description,
    image_url
  } = req.body;

  const issueId = uuidv4();
  const issueData = {
    id: issueId,
    type,
    location_name,
    latitude,
    longitude,
    severity,
    description,
    primary_image_url: image_url,
    reported_by: req.userId,
    status: 'open',
    verified: false,
    verification_count: 0,
    country: 'Zimbabwe',
    created_at: new Date().toISOString()
  };

  await db('road_issues').insert(issueData);

  const issue = await db('road_issues')
    .select('*')
    .where('id', issueId)
    .first();

  logger.logBusinessEvent('Road issue reported', {
    issueId,
    type,
    severity,
    userId: req.userId,
    location: { latitude, longitude }
  });

  res.status(201).json({
    message: 'Issue created successfully',
    issue
  });
}));

module.exports = router;
