/**
 * Production Database Configuration
 * Supports PostgreSQL, MySQL, and SQLite with connection pooling
 */

const knex = require('knex');
const path = require('path');

// Database configuration based on environment
const getDatabaseConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  
  // Production PostgreSQL configuration
  if (env === 'production' && process.env.DATABASE_URL) {
    return {
      client: 'postgresql',
      connection: process.env.DATABASE_URL,
      pool: {
        min: 2,
        max: 20,
        acquireTimeoutMillis: 60000,
        idleTimeoutMillis: 600000
      },
      migrations: {
        directory: path.join(__dirname, '../database/migrations'),
        tableName: 'knex_migrations'
      },
      seeds: {
        directory: path.join(__dirname, '../database/seeds')
      }
    };
  }
  
  // Development/Testing SQLite configuration
  return {
    client: 'better-sqlite3',
    connection: {
      filename: path.join(__dirname, '../../data/roadpulse.db')
    },
    useNullAsDefault: true,
    pool: {
      min: 1,
      max: 5
    },
    migrations: {
      directory: path.join(__dirname, '../database/migrations'),
      tableName: 'knex_migrations'
    },
    seeds: {
      directory: path.join(__dirname, '../database/seeds')
    }
  };
};

// Initialize database connection
const db = knex(getDatabaseConfig());

// Test database connection
const testConnection = async () => {
  try {
    await db.raw('SELECT 1');
    console.log('✅ Database connection established');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
};

// Initialize database (create tables if they don't exist)
const initializeDatabase = async () => {
  try {
    console.log('🔧 Initializing database schema...');
    
    // Run migrations
    await db.migrate.latest();
    console.log('✅ Database migrations completed');
    
    // Run seeds in development
    if (process.env.NODE_ENV === 'development') {
      await db.seed.run();
      console.log('✅ Database seeds completed');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
};

// Graceful shutdown
const closeDatabase = async () => {
  try {
    await db.destroy();
    console.log('✅ Database connection closed');
  } catch (error) {
    console.error('❌ Error closing database:', error);
  }
};

module.exports = {
  db,
  testConnection,
  initializeDatabase,
  closeDatabase
};
