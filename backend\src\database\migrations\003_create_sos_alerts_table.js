/**
 * Create SOS Alerts Table Migration
 */

exports.up = function(knex) {
  return knex.schema.createTable('sos_alerts', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('(lower(hex(randomblob(4))) || "-" || lower(hex(randomblob(2))) || "-4" || substr(lower(hex(randomblob(2))),2) || "-" || substr("89ab",abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || "-" || lower(hex(randomblob(6))))'));
    
    // User and emergency details
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.string('category').notNullable(); // 'medical', 'mechanical', 'security', 'accident', 'other'
    table.string('subcategory').nullable(); // More specific categorization
    table.string('severity').defaultTo('medium'); // 'low', 'medium', 'high', 'critical'
    table.string('status').defaultTo('active'); // 'active', 'responded', 'resolved', 'cancelled', 'false_alarm'
    
    // Location information
    table.decimal('latitude', 10, 8).notNullable();
    table.decimal('longitude', 11, 8).notNullable();
    table.string('location_address').nullable();
    table.string('landmark').nullable();
    table.string('district').nullable();
    table.string('city').nullable();
    table.string('country').defaultTo('Zimbabwe');
    
    // Emergency details
    table.text('description').notNullable();
    table.integer('people_count').nullable(); // Number of people involved
    table.boolean('injuries_reported').defaultTo(false);
    table.boolean('vehicle_involved').defaultTo(false);
    table.json('emergency_contacts').nullable(); // Array of emergency contact numbers
    
    // Risk assessment
    table.decimal('risk_score', 3, 2).defaultTo(0.0); // 0.0 to 1.0
    table.decimal('fraud_probability', 3, 2).defaultTo(0.0); // 0.0 to 1.0
    table.boolean('verified_emergency').defaultTo(false);
    table.json('risk_factors').nullable(); // Array of risk indicators
    
    // Response tracking
    table.integer('response_count').defaultTo(0);
    table.json('responders').nullable(); // Array of responder user IDs
    table.uuid('primary_responder').references('id').inTable('users').onDelete('SET NULL');
    table.timestamp('first_response_at').nullable();
    table.timestamp('resolved_at').nullable();
    table.text('resolution_notes').nullable();
    
    // Communication
    table.json('messages').nullable(); // Array of messages/updates
    table.string('emergency_number_called').nullable();
    table.boolean('authorities_notified').defaultTo(false);
    table.timestamp('authorities_notified_at').nullable();
    
    // Media and evidence
    table.json('images').nullable(); // Array of image URLs
    table.json('audio_recordings').nullable(); // Array of audio URLs
    table.string('live_stream_url').nullable();
    
    // Metrics and analytics
    table.integer('view_count').defaultTo(0);
    table.decimal('average_response_time', 8, 2).nullable(); // In minutes
    table.json('response_times').nullable(); // Array of response time records
    
    // Timestamps
    table.timestamps(true, true);
    table.timestamp('last_ping_at').defaultTo(knex.fn.now());
    
    // Indexes for emergency response performance
    table.index(['user_id']);
    table.index(['status']);
    table.index(['category']);
    table.index(['severity']);
    table.index(['latitude', 'longitude']);
    table.index(['created_at']);
    table.index(['risk_score']);
    table.index(['verified_emergency']);
    table.index(['city', 'country']);
    table.index(['status', 'created_at']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('sos_alerts');
};
