/**
 * WebSocket Service
 * Real-time communication for RoadPulse
 */

const jwt = require('jsonwebtoken');
const config = require('../config/environment');
const logger = require('../utils/logger');

let io = null;

/**
 * Initialize WebSocket server
 */
const initialize = (socketIO) => {
  io = socketIO;
  
  // Authentication middleware for WebSocket
  io.use((socket, next) => {
    try {
      const token = socket.handshake.auth.token || 
                   socket.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        logger.logSecurity('WebSocket connection without token', {
          socketId: socket.id,
          ip: socket.handshake.address
        });
        return next(new Error('Authentication required'));
      }

      const decoded = jwt.verify(token, config.JWT_SECRET);
      socket.userId = decoded.userId;
      
      logger.debug('WebSocket authenticated', {
        socketId: socket.id,
        userId: decoded.userId
      });
      
      next();
    } catch (error) {
      logger.logSecurity('WebSocket authentication failed', {
        socketId: socket.id,
        error: error.message,
        ip: socket.handshake.address
      });
      next(new Error('Authentication failed'));
    }
  });

  // Handle connections
  io.on('connection', (socket) => {
    logger.info(`WebSocket connected: ${socket.id} (User: ${socket.userId})`);

    // Join user to their personal room
    if (socket.userId) {
      socket.join(`user:${socket.userId}`);
      socket.join('authenticated_users');
    }

    // Handle location updates
    socket.on('location:update', (data) => {
      if (!socket.userId) return;

      logger.debug(`Location update from ${socket.userId}`, {
        latitude: data.latitude,
        longitude: data.longitude,
        accuracy: data.accuracy
      });
      
      // Broadcast location to nearby users (implement geofencing logic)
      socket.broadcast.emit('location:nearby', {
        userId: socket.userId,
        ...data,
        timestamp: new Date().toISOString()
      });
    });

    // Handle road issue reports
    socket.on('issue:report', (data) => {
      if (!socket.userId) return;

      logger.info(`Issue reported by ${socket.userId}`, {
        type: data.type,
        severity: data.severity,
        location: data.location
      });
      
      // Broadcast to all connected clients
      io.emit('issue:new', {
        id: generateId(),
        reportedBy: socket.userId,
        ...data,
        timestamp: new Date().toISOString()
      });
    });

    // Handle SOS alerts
    socket.on('sos:alert', (data) => {
      if (!socket.userId) return;

      logger.warn(`SOS alert from ${socket.userId}`, {
        category: data.category,
        severity: data.severity,
        location: data.location
      });
      
      // Broadcast SOS to all users (in production, implement geofencing)
      io.emit('sos:new', {
        id: generateId(),
        userId: socket.userId,
        ...data,
        timestamp: new Date().toISOString()
      });
    });

    // Handle SOS response
    socket.on('sos:respond', (data) => {
      if (!socket.userId) return;

      logger.info(`SOS response from ${socket.userId}`, {
        sosId: data.sosId,
        message: data.message
      });
      
      // Notify the SOS requester and other responders
      io.emit('sos:response', {
        sosId: data.sosId,
        responderId: socket.userId,
        message: data.message,
        eta: data.eta,
        timestamp: new Date().toISOString()
      });
    });

    // Handle issue verification
    socket.on('issue:verify', (data) => {
      if (!socket.userId) return;

      logger.info(`Issue verification from ${socket.userId}`, {
        issueId: data.issueId,
        verification: data.verification
      });
      
      // Broadcast verification update
      io.emit('issue:verified', {
        issueId: data.issueId,
        verifiedBy: socket.userId,
        verification: data.verification,
        notes: data.notes,
        timestamp: new Date().toISOString()
      });
    });

    // Handle navigation events
    socket.on('navigation:start', (data) => {
      if (!socket.userId) return;

      logger.info(`Navigation started by ${socket.userId}`, {
        routeId: data.routeId,
        from: data.from,
        to: data.to
      });
      
      // Join navigation room for route-specific updates
      socket.join(`route:${data.routeId}`);
      
      // Broadcast navigation start
      socket.broadcast.emit('navigation:user_started', {
        userId: socket.userId,
        ...data,
        timestamp: new Date().toISOString()
      });
    });

    // Handle navigation updates
    socket.on('navigation:update', (data) => {
      if (!socket.userId) return;

      // Send updates to users on the same route
      socket.to(`route:${data.routeId}`).emit('navigation:route_update', {
        userId: socket.userId,
        ...data,
        timestamp: new Date().toISOString()
      });
    });

    // Handle navigation completion
    socket.on('navigation:complete', (data) => {
      if (!socket.userId) return;

      logger.info(`Navigation completed by ${socket.userId}`, {
        routeId: data.routeId,
        duration: data.duration
      });
      
      // Leave navigation room
      socket.leave(`route:${data.routeId}`);
      
      // Broadcast completion
      socket.broadcast.emit('navigation:user_completed', {
        userId: socket.userId,
        ...data,
        timestamp: new Date().toISOString()
      });
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`WebSocket disconnected: ${socket.id} (User: ${socket.userId}) - Reason: ${reason}`);
      
      // Broadcast user offline status
      if (socket.userId) {
        socket.broadcast.emit('user:offline', {
          userId: socket.userId,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Handle errors
    socket.on('error', (error) => {
      logger.logError(error, null, {
        socketId: socket.id,
        userId: socket.userId
      });
    });
  });

  logger.info('✅ WebSocket server initialized');
};

/**
 * Broadcast message to all connected users
 */
const broadcast = (event, data) => {
  if (io) {
    io.emit(event, {
      ...data,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Send message to specific user
 */
const sendToUser = (userId, event, data) => {
  if (io) {
    io.to(`user:${userId}`).emit(event, {
      ...data,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Send message to users in specific location radius
 */
const sendToLocation = (latitude, longitude, radius, event, data) => {
  if (io) {
    // In a production system, you would maintain user locations
    // and calculate which users are within the radius
    io.emit(event, {
      ...data,
      location: { latitude, longitude, radius },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get connected users count
 */
const getConnectedUsersCount = () => {
  if (io) {
    return io.engine.clientsCount;
  }
  return 0;
};

/**
 * Generate unique ID for events
 */
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

module.exports = {
  initialize,
  broadcast,
  sendToUser,
  sendToLocation,
  getConnectedUsersCount
};
