/**
 * Production Authentication Middleware
 * JWT-based authentication with comprehensive security features
 */

const jwt = require('jsonwebtoken');
const { db } = require('../config/database');
const config = require('../config/environment');
const logger = require('../utils/logger');

/**
 * Generate JWT access token
 */
const generateAccessToken = (userId) => {
  return jwt.sign(
    { userId, type: 'access' },
    config.JWT_SECRET,
    { 
      expiresIn: config.JWT_EXPIRES_IN,
      issuer: 'roadpulse-api',
      audience: 'roadpulse-app'
    }
  );
};

/**
 * Generate JWT refresh token
 */
const generateRefreshToken = (userId) => {
  return jwt.sign(
    { userId, type: 'refresh' },
    config.JWT_SECRET,
    { 
      expiresIn: config.REFRESH_TOKEN_EXPIRES_IN,
      issuer: 'roadpulse-api',
      audience: 'roadpulse-app'
    }
  );
};

/**
 * Verify JWT token
 */
const verifyToken = (token, expectedType = 'access') => {
  try {
    const decoded = jwt.verify(token, config.JWT_SECRET, {
      issuer: 'roadpulse-api',
      audience: 'roadpulse-app'
    });
    
    if (decoded.type !== expectedType) {
      throw new Error(`Invalid token type. Expected ${expectedType}, got ${decoded.type}`);
    }
    
    return decoded;
  } catch (error) {
    throw new Error(`Token verification failed: ${error.message}`);
  }
};

/**
 * Main authentication middleware
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      logger.logSecurity('Missing authorization header', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.originalUrl
      });
      
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Authorization header is required',
        code: 'MISSING_AUTH_HEADER'
      });
    }

    const token = authHeader.replace('Bearer ', '');
    
    if (!token) {
      logger.logSecurity('Missing bearer token', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.originalUrl
      });
      
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Bearer token is required',
        code: 'MISSING_TOKEN'
      });
    }

    // Verify JWT token
    const decoded = verifyToken(token, 'access');
    
    // Get user from database
    const user = await db('users')
      .select([
        'id', 'email', 'name', 'is_active', 'is_verified', 
        'failed_login_attempts', 'locked_until', 'last_login_at'
      ])
      .where('id', decoded.userId)
      .first();

    if (!user) {
      logger.logSecurity('Token for non-existent user', {
        userId: decoded.userId,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(401).json({
        error: 'Invalid token',
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Check if user account is active
    if (!user.is_active) {
      logger.logSecurity('Inactive user attempted access', {
        userId: user.id,
        email: user.email,
        ip: req.ip
      });
      
      return res.status(401).json({
        error: 'Account disabled',
        message: 'Your account has been disabled',
        code: 'ACCOUNT_DISABLED'
      });
    }

    // Check if account is locked
    if (user.locked_until && new Date(user.locked_until) > new Date()) {
      logger.logSecurity('Locked user attempted access', {
        userId: user.id,
        email: user.email,
        lockedUntil: user.locked_until,
        ip: req.ip
      });
      
      return res.status(401).json({
        error: 'Account locked',
        message: 'Your account is temporarily locked due to multiple failed login attempts',
        code: 'ACCOUNT_LOCKED',
        lockedUntil: user.locked_until
      });
    }

    // Add user info to request
    req.user = user;
    req.userId = user.id;
    
    // Log successful authentication
    logger.debug('User authenticated successfully', {
      userId: user.id,
      email: user.email,
      ip: req.ip,
      url: req.originalUrl
    });
    
    next();
  } catch (error) {
    logger.logSecurity('Authentication failed', {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl
    });
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Invalid token',
        message: 'JWT token is invalid',
        code: 'INVALID_TOKEN'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expired',
        message: 'JWT token has expired',
        code: 'TOKEN_EXPIRED'
      });
    }
    
    return res.status(500).json({
      error: 'Authentication failed',
      message: 'An error occurred during authentication',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * Optional authentication middleware
 * Adds user info if token is present but doesn't require it
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return next();
    }

    const token = authHeader.replace('Bearer ', '');
    
    if (!token) {
      return next();
    }

    // Verify token
    const decoded = verifyToken(token, 'access');
    
    // Get user from database
    const user = await db('users')
      .select(['id', 'email', 'name', 'is_active'])
      .where('id', decoded.userId)
      .where('is_active', true)
      .first();

    if (user) {
      req.user = user;
      req.userId = user.id;
    }
    
    next();
  } catch (error) {
    // For optional auth, we don't fail on invalid tokens
    logger.debug('Optional auth failed', { error: error.message });
    next();
  }
};

/**
 * Admin role middleware
 */
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'You must be logged in to access this resource',
        code: 'AUTH_REQUIRED'
      });
    }

    const user = await db('users')
      .select(['is_admin'])
      .where('id', req.userId)
      .first();

    if (!user || !user.is_admin) {
      logger.logSecurity('Non-admin attempted admin access', {
        userId: req.userId,
        email: req.user.email,
        ip: req.ip,
        url: req.originalUrl
      });
      
      return res.status(403).json({
        error: 'Access denied',
        message: 'Administrator privileges required',
        code: 'ADMIN_REQUIRED'
      });
    }

    next();
  } catch (error) {
    logger.logError(error, req);
    return res.status(500).json({
      error: 'Authorization failed',
      message: 'An error occurred during authorization',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * Rate limiting for authentication endpoints
 */
const authRateLimit = require('express-rate-limit')({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: {
    error: 'Too many authentication attempts',
    message: 'Too many failed login attempts. Please try again later.',
    code: 'AUTH_RATE_LIMIT'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true,
  handler: (req, res) => {
    logger.logSecurity('Authentication rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    res.status(429).json({
      error: 'Too many authentication attempts',
      message: 'Too many failed login attempts. Please try again later.',
      code: 'AUTH_RATE_LIMIT'
    });
  }
});

module.exports = {
  authenticateToken,
  optionalAuth,
  requireAdmin,
  authRateLimit,
  generateAccessToken,
  generateRefreshToken,
  verifyToken
};
