/**
 * Location Routes for RoadPulse Backend
 */

import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { db } from '../database/connection';
import { authMiddleware } from '../middleware/auth';
import { rateLimiter } from '../middleware/rateLimiter';
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler';

const router = express.Router();

/**
 * POST /location/update
 * Update user location
 */
router.post('/update', [
  rateLimiter,
  authMiddleware,
  body('latitude').isFloat({ min: -90, max: 90 }),
  body('longitude').isFloat({ min: -180, max: 180 }),
  body('accuracy').optional().isFloat({ min: 0 }),
  body('heading').optional().isFloat({ min: 0, max: 360 }),
  body('speed').optional().isFloat({ min: 0 })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { latitude, longitude, accuracy, heading, speed } = req.body;

  await db('user_locations').insert({
    user_id: req.userId,
    latitude,
    longitude,
    accuracy,
    heading,
    speed,
    recorded_at: new Date()
  });

  res.json({
    message: 'Location updated successfully',
    timestamp: new Date()
  });
}));

/**
 * GET /location/nearby
 * Get nearby users (for emergency/help features)
 */
router.get('/nearby', [
  rateLimiter,
  authMiddleware,
  query('lat').isFloat({ min: -90, max: 90 }),
  query('lng').isFloat({ min: -180, max: 180 }),
  query('radius').optional().isFloat({ min: 0.1, max: 10 })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { lat, lng, radius = 2 } = req.query;
  const latNum = Number(lat);
  const lngNum = Number(lng);
  const radiusNum = Number(radius);

  // Get recent locations of nearby users (within last 10 minutes)
  const nearbyUsers = await db('user_locations')
    .join('users', 'user_locations.user_id', 'users.id')
    .select([
      'users.id', 'users.name', 'users.trust_score',
      'user_locations.latitude', 'user_locations.longitude',
      'user_locations.recorded_at'
    ])
    .where('user_locations.user_id', '!=', req.userId)
    .where('user_locations.recorded_at', '>', new Date(Date.now() - 10 * 60 * 1000))
    .whereRaw(`
      (6371 * acos(
        cos(radians(?)) * cos(radians(user_locations.latitude)) * 
        cos(radians(user_locations.longitude) - radians(?)) + 
        sin(radians(?)) * sin(radians(user_locations.latitude))
      )) <= ?
    `, [latNum, lngNum, latNum, radiusNum])
    .orderBy('user_locations.recorded_at', 'desc');

  res.json({
    nearby_users: nearbyUsers,
    search_radius: radiusNum,
    center: { lat: latNum, lng: lngNum }
  });
}));

export default router;
