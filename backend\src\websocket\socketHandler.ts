/**
 * WebSocket Handler for RoadPulse Backend
 */

import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

interface AuthenticatedSocket extends Socket {
  userId?: string;
}

/**
 * Setup WebSocket server with authentication and event handlers
 */
export const setupWebSocket = (io: SocketIOServer): void => {
  // Authentication middleware for WebSocket
  io.use((socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        logger.warn('WebSocket connection attempted without token');
        return next(new Error('Authentication required'));
      }

      const decoded = jwt.verify(token, JWT_SECRET) as { userId: string };
      socket.userId = decoded.userId;
      
      logger.info(`WebSocket authenticated for user: ${decoded.userId}`);
      next();
    } catch (error) {
      logger.error('WebSocket authentication failed:', error);
      next(new Error('Authentication failed'));
    }
  });

  // Handle connections
  io.on('connection', (socket: AuthenticatedSocket) => {
    logger.info(`WebSocket connected: ${socket.id} (User: ${socket.userId})`);

    // Join user to their personal room
    if (socket.userId) {
      socket.join(`user:${socket.userId}`);
    }

    // Handle location updates
    socket.on('location:update', (data: {
      latitude: number;
      longitude: number;
      accuracy: number;
      heading?: number;
      speed?: number;
    }) => {
      if (!socket.userId) return;

      logger.debug(`Location update from ${socket.userId}:`, data);
      
      // Broadcast location to nearby users (implement geofencing logic)
      socket.broadcast.emit('location:nearby', {
        userId: socket.userId,
        ...data,
        timestamp: new Date()
      });

      // Store location in database (implement as needed)
      // await storeUserLocation(socket.userId, data);
    });

    // Handle road issue reports
    socket.on('issue:report', (data: {
      type: string;
      location: { lat: number; lng: number };
      severity: string;
      description: string;
    }) => {
      if (!socket.userId) return;

      logger.info(`Issue reported by ${socket.userId}:`, data);
      
      // Broadcast to all connected clients
      io.emit('issue:new', {
        id: generateId(),
        reportedBy: socket.userId,
        ...data,
        timestamp: new Date()
      });
    });

    // Handle SOS alerts
    socket.on('sos:alert', (data: {
      category: string;
      description: string;
      location: { lat: number; lng: number };
      severity: 'low' | 'medium' | 'high' | 'critical';
    }) => {
      if (!socket.userId) return;

      logger.warn(`SOS alert from ${socket.userId}:`, data);
      
      // Broadcast SOS to all users (in production, implement geofencing)
      io.emit('sos:new', {
        id: generateId(),
        userId: socket.userId,
        ...data,
        timestamp: new Date()
      });

      // Send to emergency services (implement as needed)
      // await notifyEmergencyServices(socket.userId, data);
    });

    // Handle SOS response
    socket.on('sos:respond', (data: {
      sosId: string;
      message: string;
      eta?: number;
    }) => {
      if (!socket.userId) return;

      logger.info(`SOS response from ${socket.userId}:`, data);
      
      // Notify the SOS requester
      io.emit('sos:response', {
        sosId: data.sosId,
        responderId: socket.userId,
        message: data.message,
        eta: data.eta,
        timestamp: new Date()
      });
    });

    // Handle issue verification
    socket.on('issue:verify', (data: {
      issueId: string;
      verification: 'confirm' | 'dispute';
      notes?: string;
    }) => {
      if (!socket.userId) return;

      logger.info(`Issue verification from ${socket.userId}:`, data);
      
      // Broadcast verification update
      io.emit('issue:verified', {
        issueId: data.issueId,
        verifiedBy: socket.userId,
        verification: data.verification,
        notes: data.notes,
        timestamp: new Date()
      });
    });

    // Handle navigation updates
    socket.on('navigation:start', (data: {
      from: { lat: number; lng: number };
      to: { lat: number; lng: number };
      routeId: string;
    }) => {
      if (!socket.userId) return;

      logger.info(`Navigation started by ${socket.userId}:`, data);
      
      // Join navigation room for route-specific updates
      socket.join(`route:${data.routeId}`);
      
      // Broadcast navigation start
      socket.broadcast.emit('navigation:user_started', {
        userId: socket.userId,
        ...data,
        timestamp: new Date()
      });
    });

    // Handle navigation updates
    socket.on('navigation:update', (data: {
      routeId: string;
      currentLocation: { lat: number; lng: number };
      progress: number;
      eta: number;
    }) => {
      if (!socket.userId) return;

      // Send updates to users on the same route
      socket.to(`route:${data.routeId}`).emit('navigation:route_update', {
        userId: socket.userId,
        ...data,
        timestamp: new Date()
      });
    });

    // Handle navigation completion
    socket.on('navigation:complete', (data: {
      routeId: string;
      duration: number;
      distance: number;
    }) => {
      if (!socket.userId) return;

      logger.info(`Navigation completed by ${socket.userId}:`, data);
      
      // Leave navigation room
      socket.leave(`route:${data.routeId}`);
      
      // Broadcast completion
      socket.broadcast.emit('navigation:user_completed', {
        userId: socket.userId,
        ...data,
        timestamp: new Date()
      });
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`WebSocket disconnected: ${socket.id} (User: ${socket.userId}) - Reason: ${reason}`);
      
      // Broadcast user offline status
      if (socket.userId) {
        socket.broadcast.emit('user:offline', {
          userId: socket.userId,
          timestamp: new Date()
        });
      }
    });

    // Handle errors
    socket.on('error', (error) => {
      logger.error(`WebSocket error for ${socket.id}:`, error);
    });
  });

  logger.info('✅ WebSocket server configured');
};

/**
 * Generate unique ID for events
 */
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};
