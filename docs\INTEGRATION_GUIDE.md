# 🚀 RoadPulse Integration Guide

## Quick Start: From Mock to Real Data

### Step 1: Environment Setup

1. **Copy environment files:**
   ```bash
   cp .env.example .env.local
   cp .env.development .env.development.local
   ```

2. **Configure for development:**
   ```bash
   # .env.development.local
   REACT_APP_USE_MOCK_DATA=true
   REACT_APP_ENABLE_REALTIME=false
   REACT_APP_API_URL=http://localhost:3001/v1
   ```

3. **Configure for production:**
   ```bash
   # .env.local
   REACT_APP_USE_MOCK_DATA=false
   REACT_APP_ENABLE_REALTIME=true
   REACT_APP_API_URL=https://your-api-domain.com/v1
   ```

### Step 2: Update Components to Use DataService

Replace direct mock data usage with the new DataService:

```typescript
// Before (using mock data)
import { defaultIssues } from './mockData';

// After (using DataService)
import { dataService } from '../services/dataService';

// In your component
useEffect(() => {
  const loadIssues = async () => {
    try {
      const issues = await dataService.getIssues();
      setIssues(issues);
    } catch (error) {
      console.error('Failed to load issues:', error);
    }
  };
  
  loadIssues();
}, []);
```

### Step 3: Initialize DataService

Add initialization to your main App component:

```typescript
// src/App.tsx
import { dataService } from './services/dataService';
import { useEffect, useState } from 'react';

function App() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [user, setUser] = useState(null);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize data service
        await dataService.initialize(user?.id);
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        // App will fall back to mock data
        setIsInitialized(true);
      }
    };

    initializeApp();
  }, [user]);

  if (!isInitialized) {
    return <div>Loading...</div>;
  }

  return (
    // Your app components
  );
}
```

### Step 4: Backend Setup (Optional for Development)

If you want to set up the backend:

1. **Install dependencies:**
   ```bash
   cd backend
   npm install
   ```

2. **Set up database:**
   ```bash
   # Install PostgreSQL with PostGIS
   docker run --name roadpulse-db \
     -e POSTGRES_PASSWORD=password \
     -e POSTGRES_DB=roadpulse \
     -p 5432:5432 \
     -d postgis/postgis:13-3.1
   ```

3. **Configure environment:**
   ```bash
   # backend/.env
   DATABASE_URL=postgresql://postgres:password@localhost:5432/roadpulse
   JWT_SECRET=your_jwt_secret_here
   PORT=3001
   ```

4. **Run migrations:**
   ```bash
   npm run migrate
   npm run seed
   ```

5. **Start development server:**
   ```bash
   npm run dev
   ```

## Component Updates

### Update InteractiveMap Component

```typescript
// src/components/InteractiveMap.tsx
import { dataService } from '../services/dataService';

const InteractiveMap = () => {
  const [issues, setIssues] = useState<RoadIssue[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadIssues = async () => {
      try {
        setLoading(true);
        const issuesData = await dataService.getIssues();
        setIssues(issuesData);
      } catch (error) {
        console.error('Failed to load issues:', error);
      } finally {
        setLoading(false);
      }
    };

    loadIssues();
  }, []);

  // Rest of your component...
};
```

### Update NavigationMode Component

```typescript
// src/components/NavigationMode.tsx
import { dataService } from '../services/dataService';
import { realtimeService } from '../services/realtime';

const NavigationMode = ({ issues: propIssues, ...props }) => {
  const [issues, setIssues] = useState<RoadIssue[]>(propIssues || []);

  useEffect(() => {
    // Load real-time issues if no props provided
    if (!propIssues) {
      const loadIssues = async () => {
        try {
          const issuesData = await dataService.getNearbyIssues(
            navState.currentPosition[0],
            navState.currentPosition[1],
            10 // 10km radius
          );
          setIssues(issuesData);
        } catch (error) {
          console.error('Failed to load nearby issues:', error);
        }
      };

      loadIssues();
    }

    // Subscribe to real-time updates
    const unsubscribe = realtimeService.subscribeToNavigationUpdates((data) => {
      // Handle real-time navigation updates
      console.log('Navigation update:', data);
    });

    return unsubscribe;
  }, [propIssues, navState.currentPosition]);

  // Rest of your component...
};
```

### Update SOS Component

```typescript
// src/pages/SOS.tsx
import { dataService } from '../services/dataService';

const SOS = () => {
  const [helpers, setHelpers] = useState<SOSHelper[]>([]);

  const handleCreateSOS = async (sosData) => {
    try {
      const sosAlert = await dataService.createSOS({
        location: {
          lat: currentLocation.lat,
          lng: currentLocation.lng,
          accuracy: 10,
          address: 'Current Location'
        },
        category: sosData.category,
        description: sosData.description,
        urgency_level: sosData.urgency
      });

      // Navigate to SOS tracking page
      navigate(`/sos/${sosAlert.id}`);
    } catch (error) {
      console.error('Failed to create SOS:', error);
    }
  };

  // Rest of your component...
};
```

## Real-time Features

### Location Tracking

```typescript
// src/hooks/useLocationTracking.ts
import { useEffect } from 'react';
import { dataService } from '../services/dataService';

export const useLocationTracking = (enabled: boolean) => {
  useEffect(() => {
    if (!enabled) return;

    const watchId = navigator.geolocation.watchPosition(
      async (position) => {
        try {
          await dataService.updateLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
            accuracy: position.coords.accuracy,
            heading: position.coords.heading || undefined,
            speed: position.coords.speed || undefined,
          });
        } catch (error) {
          console.error('Failed to update location:', error);
        }
      },
      (error) => console.error('Location error:', error),
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 30000
      }
    );

    return () => navigator.geolocation.clearWatch(watchId);
  }, [enabled]);
};
```

## Testing the Integration

### 1. Development Mode (Mock Data)
```bash
# Start with mock data
REACT_APP_USE_MOCK_DATA=true npm start
```

### 2. API Mode (Real Backend)
```bash
# Start backend
cd backend && npm run dev

# Start frontend with API
REACT_APP_USE_MOCK_DATA=false npm start
```

### 3. Production Mode
```bash
# Build and deploy
npm run build
```

## Deployment

### Frontend Deployment (Vercel/Netlify)

1. **Set environment variables:**
   ```
   REACT_APP_USE_MOCK_DATA=false
   REACT_APP_API_URL=https://your-api.com/v1
   REACT_APP_WS_URL=wss://your-api.com
   ```

2. **Deploy:**
   ```bash
   npm run build
   # Deploy dist folder
   ```

### Backend Deployment (Railway/Heroku/DigitalOcean)

1. **Set environment variables:**
   ```
   DATABASE_URL=postgresql://...
   JWT_SECRET=your_secret
   FRONTEND_URL=https://your-frontend.com
   ```

2. **Deploy with Docker:**
   ```dockerfile
   # backend/Dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --only=production
   COPY . .
   RUN npm run build
   EXPOSE 3001
   CMD ["npm", "start"]
   ```

## Monitoring & Analytics

### Add Error Tracking
```typescript
// src/utils/errorTracking.ts
import * as Sentry from '@sentry/react';

if (process.env.REACT_APP_SENTRY_DSN) {
  Sentry.init({
    dsn: process.env.REACT_APP_SENTRY_DSN,
    environment: process.env.NODE_ENV,
  });
}
```

### Add Analytics
```typescript
// src/utils/analytics.ts
import { gtag } from 'ga-gtag';

export const trackEvent = (action: string, category: string, label?: string) => {
  if (process.env.REACT_APP_GOOGLE_ANALYTICS_ID) {
    gtag('event', action, {
      event_category: category,
      event_label: label,
    });
  }
};
```

## Next Steps

1. **Start with mock data** to ensure everything works
2. **Set up basic backend** with PostgreSQL
3. **Implement authentication** system
4. **Add real-time features** with WebSocket
5. **Deploy to staging** environment
6. **Test with real users** in Zimbabwe
7. **Scale and optimize** based on usage

This integration approach allows you to gradually transition from mock data to a full production system while maintaining functionality throughout the development process.
