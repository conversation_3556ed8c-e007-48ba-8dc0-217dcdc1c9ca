/**
 * Create Users Table Migration
 */

exports.up = function(knex) {
  return knex.schema.createTable('users', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('(lower(hex(randomblob(4))) || "-" || lower(hex(randomblob(2))) || "-4" || substr(lower(hex(randomblob(2))),2) || "-" || substr("89ab",abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || "-" || lower(hex(randomblob(6))))'));
    
    // Basic user information
    table.string('email').unique().notNullable();
    table.string('password').nullable(); // Nullable for OAuth users
    table.string('name').notNullable();
    table.string('first_name').nullable();
    table.string('last_name').nullable();
    table.string('phone').nullable();
    table.text('profile_image_url').nullable();
    
    // User metrics
    table.integer('trust_score').defaultTo(50);
    table.decimal('rating', 3, 2).defaultTo(0.0);
    table.boolean('is_verified').defaultTo(false);
    table.boolean('is_active').defaultTo(true);
    table.boolean('is_admin').defaultTo(false);
    
    // Authentication provider info
    table.string('provider').defaultTo('email'); // 'email', 'google', 'facebook', etc.
    table.string('provider_id').nullable();
    table.json('provider_data').nullable(); // Store additional provider data
    
    // Location and preferences
    table.string('country').nullable();
    table.string('city').nullable();
    table.string('timezone').nullable();
    table.string('language').defaultTo('en');
    table.json('preferences').nullable(); // User preferences as JSON
    
    // Security and tracking
    table.string('password_reset_token').nullable();
    table.timestamp('password_reset_expires').nullable();
    table.string('email_verification_token').nullable();
    table.timestamp('email_verified_at').nullable();
    table.timestamp('last_login_at').nullable();
    table.string('last_login_ip').nullable();
    table.integer('failed_login_attempts').defaultTo(0);
    table.timestamp('locked_until').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['email']);
    table.index(['provider', 'provider_id']);
    table.index(['is_active']);
    table.index(['created_at']);
    table.index(['trust_score']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('users');
};
