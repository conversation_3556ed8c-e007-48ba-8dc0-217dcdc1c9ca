/**
 * SOS Routes for RoadPulse Backend
 */

import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { db } from '../database/connection';
import { authMiddleware } from '../middleware/auth';
import { sosRateLimiter } from '../middleware/rateLimiter';
import { asyncHandler } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * POST /sos
 * Create SOS alert
 */
router.post('/', [
  sosRateLimiter,
  authMiddleware,
  body('category').isIn(['medical', 'mechanical', 'security', 'other']),
  body('description').trim().isLength({ min: 10, max: 500 }),
  body('latitude').isFloat({ min: -90, max: 90 }),
  body('longitude').isFloat({ min: -180, max: 180 }),
  body('location_address').optional().trim().isLength({ max: 255 })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const {
    category,
    description,
    latitude,
    longitude,
    location_address
  } = req.body;

  const [alertId] = await db('sos_alerts').insert({
    user_id: req.userId,
    category,
    description,
    latitude,
    longitude,
    location_address,
    status: 'active',
    risk_score: 0.3, // Default risk score
    fraud_probability: 0.1 // Default fraud probability
  }).returning('id');

  const alert = await db('sos_alerts')
    .select('*')
    .where('id', alertId)
    .first();

  logger.warn(`SOS alert created: ${alertId} by user ${req.userId}`);

  res.status(201).json({
    message: 'SOS alert created successfully',
    alert
  });
}));

/**
 * GET /sos/active
 * Get active SOS alerts
 */
router.get('/active', [
  authMiddleware,
  query('lat').optional().isFloat(),
  query('lng').optional().isFloat(),
  query('radius').optional().isFloat({ min: 0.1, max: 50 })
], asyncHandler(async (req, res) => {
  const { lat, lng, radius = 10 } = req.query;

  let query = db('sos_alerts')
    .select('*')
    .where('status', 'active')
    .orderBy('created_at', 'desc');

  // Geographic filtering
  if (lat && lng) {
    const latNum = Number(lat);
    const lngNum = Number(lng);
    const radiusNum = Number(radius);
    
    query = query.whereRaw(`
      (6371 * acos(
        cos(radians(?)) * cos(radians(latitude)) * 
        cos(radians(longitude) - radians(?)) + 
        sin(radians(?)) * sin(radians(latitude))
      )) <= ?
    `, [latNum, lngNum, latNum, radiusNum]);
  }

  const alerts = await query;

  res.json({ alerts });
}));

export default router;
