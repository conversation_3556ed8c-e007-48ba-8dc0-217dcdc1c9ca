/**
 * User Routes
 * User profile and account management
 */

const express = require('express');
const { validationSets } = require('../middleware/validation');
const { db } = require('../config/database');
const logger = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

/**
 * GET /users/profile
 * Get current user profile
 */
router.get('/profile', asyncHandler(async (req, res) => {
  const user = await db('users')
    .select([
      'id', 'email', 'name', 'first_name', 'last_name', 'phone',
      'profile_image_url', 'trust_score', 'rating', 'is_verified',
      'provider', 'country', 'city', 'preferences', 'created_at', 'last_login_at'
    ])
    .where('id', req.userId)
    .first();

  if (!user) {
    return res.status(404).json({
      error: 'User not found',
      message: 'User profile not found'
    });
  }

  res.json({
    id: user.id,
    email: user.email,
    name: user.name,
    firstName: user.first_name,
    lastName: user.last_name,
    phone: user.phone,
    profileImage: user.profile_image_url,
    trustScore: user.trust_score,
    rating: parseFloat(user.rating) || 0.0,
    isVerified: Boolean(user.is_verified),
    provider: user.provider,
    location: {
      country: user.country,
      city: user.city
    },
    preferences: user.preferences ? JSON.parse(user.preferences) : {
      notifications: true,
      locationSharing: true,
      emergencyContacts: []
    },
    createdAt: user.created_at,
    lastLoginAt: user.last_login_at
  });
}));

/**
 * PUT /users/profile
 * Update user profile
 */
router.put('/profile', [
  ...validationSets.register.slice(2, -1), // Use name and phone validation
  asyncHandler(async (req, res) => {
    const updates = {};
    const { name, phone, firstName, lastName, city, preferences } = req.body;

    if (name) updates.name = name.trim();
    if (phone) updates.phone = phone;
    if (firstName) updates.first_name = firstName.trim();
    if (lastName) updates.last_name = lastName.trim();
    if (city) updates.city = city.trim();
    if (preferences) updates.preferences = JSON.stringify(preferences);

    if (Object.keys(updates).length === 0) {
      return res.status(400).json({
        error: 'No updates provided',
        message: 'At least one field must be provided for update'
      });
    }

    await db('users').where('id', req.userId).update(updates);

    const updatedUser = await db('users')
      .select([
        'id', 'email', 'name', 'first_name', 'last_name', 'phone',
        'profile_image_url', 'trust_score', 'rating', 'is_verified',
        'provider', 'country', 'city', 'preferences', 'created_at', 'last_login_at'
      ])
      .where('id', req.userId)
      .first();

    logger.logBusinessEvent('User profile updated', {
      userId: req.userId,
      updatedFields: Object.keys(updates)
    });

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        firstName: updatedUser.first_name,
        lastName: updatedUser.last_name,
        phone: updatedUser.phone,
        profileImage: updatedUser.profile_image_url,
        trustScore: updatedUser.trust_score,
        rating: parseFloat(updatedUser.rating) || 0.0,
        isVerified: Boolean(updatedUser.is_verified),
        provider: updatedUser.provider,
        location: {
          country: updatedUser.country,
          city: updatedUser.city
        },
        preferences: updatedUser.preferences ? JSON.parse(updatedUser.preferences) : {
          notifications: true,
          locationSharing: true,
          emergencyContacts: []
        },
        createdAt: updatedUser.created_at,
        lastLoginAt: updatedUser.last_login_at
      }
    });
  })
]);

/**
 * GET /users/stats
 * Get user statistics
 */
router.get('/stats', asyncHandler(async (req, res) => {
  // Get user's reported issues count
  const issuesReported = await db('road_issues')
    .count('* as count')
    .where('reported_by', req.userId)
    .first();

  // Get user's verified issues count
  const issuesVerified = await db('road_issues')
    .count('* as count')
    .where('reported_by', req.userId)
    .where('verified', true)
    .first();

  // Get user's SOS alerts count
  const sosAlerts = await db('sos_alerts')
    .count('* as count')
    .where('user_id', req.userId)
    .first();

  // Get user's recent activity (last 30 days)
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
  
  const recentIssues = await db('road_issues')
    .count('* as count')
    .where('reported_by', req.userId)
    .where('created_at', '>', thirtyDaysAgo)
    .first();

  res.json({
    issuesReported: parseInt(issuesReported.count) || 0,
    issuesVerified: parseInt(issuesVerified.count) || 0,
    sosAlerts: parseInt(sosAlerts.count) || 0,
    recentActivity: {
      issuesLast30Days: parseInt(recentIssues.count) || 0
    },
    trustScore: req.user.trust_score || 50,
    rating: parseFloat(req.user.rating) || 0.0
  });
}));

module.exports = router;
