/**
 * Database Connection for RoadPulse Backend
 */

import knex, { Knex } from 'knex';
import { logger } from '../utils/logger';

// Database configuration
const dbConfig: Knex.Config = {
  client: 'postgresql',
  connection: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'roadpulse'
  },
  pool: {
    min: 2,
    max: 10
  },
  migrations: {
    tableName: 'knex_migrations',
    directory: './migrations'
  },
  seeds: {
    directory: './seeds'
  }
};

// Create database instance
export const db = knex(dbConfig);

/**
 * Initialize database connection and create tables if they don't exist
 */
export const initializeDatabase = async (): Promise<void> => {
  try {
    // Test database connection
    await db.raw('SELECT 1');
    logger.info('✅ Database connection established');

    // Create tables if they don't exist
    await createTables();
    logger.info('✅ Database tables verified');

  } catch (error) {
    logger.error('❌ Database initialization failed:', error);
    
    // For development, create a simple in-memory fallback
    if (process.env.NODE_ENV === 'development') {
      logger.warn('🔄 Falling back to SQLite for development');
      await initializeSQLiteFallback();
    } else {
      throw error;
    }
  }
};

/**
 * Create database tables
 */
const createTables = async (): Promise<void> => {
  // Users table
  const hasUsersTable = await db.schema.hasTable('users');
  if (!hasUsersTable) {
    await db.schema.createTable('users', (table) => {
      table.uuid('id').primary().defaultTo(db.raw('gen_random_uuid()'));
      table.string('email').unique().notNullable();
      table.string('password').nullable(); // Nullable for OAuth users
      table.string('name').notNullable();
      table.string('first_name').nullable();
      table.string('last_name').nullable();
      table.string('phone').nullable();
      table.string('profile_image_url').nullable();
      table.integer('trust_score').defaultTo(50);
      table.decimal('rating', 3, 2).defaultTo(0.0);
      table.boolean('is_verified').defaultTo(false);
      table.string('provider').defaultTo('email'); // 'email', 'google', etc.
      table.string('provider_id').nullable();
      table.timestamps(true, true);
      table.timestamp('last_login_at').nullable();
      
      // Indexes
      table.index(['email']);
      table.index(['provider', 'provider_id']);
    });
    logger.info('✅ Created users table');
  }

  // Road issues table
  const hasIssuesTable = await db.schema.hasTable('road_issues');
  if (!hasIssuesTable) {
    await db.schema.createTable('road_issues', (table) => {
      table.uuid('id').primary().defaultTo(db.raw('gen_random_uuid()'));
      table.string('type').notNullable(); // 'pothole', 'construction', 'accident', etc.
      table.string('location_name').notNullable();
      table.decimal('latitude', 10, 8).notNullable();
      table.decimal('longitude', 11, 8).notNullable();
      table.string('severity').notNullable(); // 'low', 'medium', 'high', 'critical'
      table.text('description').notNullable();
      table.string('image_url').nullable();
      table.uuid('reported_by').references('id').inTable('users').onDelete('SET NULL');
      table.string('status').defaultTo('open'); // 'open', 'in_progress', 'resolved', 'closed'
      table.boolean('verified').defaultTo(false);
      table.integer('verification_count').defaultTo(0);
      table.timestamps(true, true);
      
      // Indexes
      table.index(['type']);
      table.index(['status']);
      table.index(['latitude', 'longitude']);
      table.index(['created_at']);
    });
    logger.info('✅ Created road_issues table');
  }

  // SOS alerts table
  const hasSOSTable = await db.schema.hasTable('sos_alerts');
  if (!hasSOSTable) {
    await db.schema.createTable('sos_alerts', (table) => {
      table.uuid('id').primary().defaultTo(db.raw('gen_random_uuid()'));
      table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
      table.string('category').notNullable(); // 'medical', 'mechanical', 'security', 'other'
      table.text('description').notNullable();
      table.decimal('latitude', 10, 8).notNullable();
      table.decimal('longitude', 11, 8).notNullable();
      table.string('location_address').nullable();
      table.string('status').defaultTo('active'); // 'active', 'responded', 'resolved', 'cancelled'
      table.decimal('risk_score', 3, 2).defaultTo(0.0);
      table.decimal('fraud_probability', 3, 2).defaultTo(0.0);
      table.timestamps(true, true);
      table.timestamp('resolved_at').nullable();
      
      // Indexes
      table.index(['user_id']);
      table.index(['status']);
      table.index(['latitude', 'longitude']);
      table.index(['created_at']);
    });
    logger.info('✅ Created sos_alerts table');
  }

  // User locations table (for tracking)
  const hasLocationsTable = await db.schema.hasTable('user_locations');
  if (!hasLocationsTable) {
    await db.schema.createTable('user_locations', (table) => {
      table.uuid('id').primary().defaultTo(db.raw('gen_random_uuid()'));
      table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
      table.decimal('latitude', 10, 8).notNullable();
      table.decimal('longitude', 11, 8).notNullable();
      table.decimal('accuracy', 8, 2).nullable();
      table.decimal('heading', 5, 2).nullable();
      table.decimal('speed', 8, 2).nullable();
      table.timestamp('recorded_at').defaultTo(db.fn.now());
      
      // Indexes
      table.index(['user_id']);
      table.index(['recorded_at']);
    });
    logger.info('✅ Created user_locations table');
  }
};

/**
 * SQLite fallback for development
 */
const initializeSQLiteFallback = async (): Promise<void> => {
  const sqliteConfig: Knex.Config = {
    client: 'sqlite3',
    connection: {
      filename: './dev.sqlite3'
    },
    useNullAsDefault: true
  };

  // Replace the global db instance
  Object.assign(db, knex(sqliteConfig));
  
  // Create tables for SQLite
  await createTables();
  logger.info('✅ SQLite fallback initialized');
};

/**
 * Close database connection
 */
export const closeDatabase = async (): Promise<void> => {
  await db.destroy();
  logger.info('Database connection closed');
};
