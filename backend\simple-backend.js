/**
 * Simple RoadPulse Backend Server
 * Quick working backend for immediate testing
 */

const express = require('express');
const cors = require('cors');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { OAuth2Client } = require('google-auth-library');
const fs = require('fs');
const path = require('path');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Simple in-memory database for testing
let users = [];
let issues = [];
let sosAlerts = [];

// Google OAuth client
const googleClient = new OAuth2Client('************-pps8qgn62lj4osfmh64u8d3armudmtle.apps.googleusercontent.com');

// JWT secret
const JWT_SECRET = 'roadpulse-secret-key-for-testing';

// Middleware
app.use(cors({
  origin: ['http://localhost:8080', 'http://localhost:8081', 'http://localhost:8082', 'http://localhost:8083'],
  credentials: true
}));
app.use(express.json());

// Helper functions
const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2);
const generateToken = (userId) => jwt.sign({ userId }, JWT_SECRET, { expiresIn: '24h' });

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.userId = user.userId;
    next();
  });
};

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'RoadPulse Backend is running',
    timestamp: new Date().toISOString(),
    database: 'In-Memory',
    version: '1.0.0'
  });
});

// API Documentation
app.get('/api/docs', (req, res) => {
  res.json({
    name: 'RoadPulse API',
    version: '1.0.0',
    description: 'Simple backend for RoadPulse testing',
    endpoints: {
      'GET /health': 'Health check',
      'POST /v1/auth/register': 'Register new user',
      'POST /v1/auth/login': 'Login user',
      'POST /v1/auth/google/register': 'Register with Google',
      'POST /v1/auth/google/login': 'Login with Google',
      'GET /v1/auth/profile': 'Get user profile',
      'GET /v1/issues': 'Get road issues',
      'POST /v1/issues': 'Create road issue',
      'GET /v1/sos/active': 'Get active SOS alerts',
      'POST /v1/sos': 'Create SOS alert'
    }
  });
});

// Email registration
app.post('/v1/auth/register', async (req, res) => {
  try {
    const { email, password, name, phone } = req.body;

    // Check if user exists
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'An account with this email already exists'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const userId = generateId();
    const user = {
      id: userId,
      email,
      password: hashedPassword,
      name,
      phone,
      trustScore: 50,
      rating: 0.0,
      isVerified: false,
      provider: 'email',
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString()
    };

    users.push(user);

    // Generate token
    const token = generateToken(userId);

    console.log(`✅ User registered: ${email}`);

    res.status(201).json({
      message: 'User registered successfully',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        phone: user.phone,
        trustScore: user.trustScore,
        rating: user.rating,
        isVerified: user.isVerified,
        provider: user.provider,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      error: 'Registration failed',
      message: 'An error occurred during registration'
    });
  }
});

// Email login
app.post('/v1/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Email or password is incorrect'
      });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Email or password is incorrect'
      });
    }

    // Update last login
    user.lastLoginAt = new Date().toISOString();

    // Generate token
    const token = generateToken(user.id);

    console.log(`✅ User logged in: ${email}`);

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        phone: user.phone,
        trustScore: user.trustScore,
        rating: user.rating,
        isVerified: user.isVerified,
        provider: user.provider,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Login failed',
      message: 'An error occurred during login'
    });
  }
});

// Google OAuth registration
app.post('/v1/auth/google/register', async (req, res) => {
  try {
    const { id_token, user_data } = req.body;

    // Verify Google ID token
    const ticket = await googleClient.verifyIdToken({
      idToken: id_token,
      audience: '************-pps8qgn62lj4osfmh64u8d3armudmtle.apps.googleusercontent.com'
    });

    const payload = ticket.getPayload();
    if (!payload) {
      return res.status(401).json({
        error: 'Invalid Google token',
        message: 'Google authentication failed'
      });
    }

    // Check if user exists
    const existingUser = users.find(u => u.email === user_data.email);
    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'An account with this email already exists'
      });
    }

    // Create user
    const userId = generateId();
    const user = {
      id: userId,
      email: user_data.email,
      name: user_data.name,
      firstName: user_data.firstName,
      lastName: user_data.lastName,
      profileImage: user_data.profileImage,
      trustScore: 60,
      rating: 0.0,
      isVerified: true,
      provider: 'google',
      providerId: user_data.providerId,
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString()
    };

    users.push(user);

    // Generate token
    const token = generateToken(userId);

    console.log(`✅ Google user registered: ${user_data.email}`);

    res.status(201).json({
      message: 'Google registration successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        firstName: user.firstName,
        lastName: user.lastName,
        profileImage: user.profileImage,
        trustScore: user.trustScore,
        rating: user.rating,
        isVerified: user.isVerified,
        provider: user.provider,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    });
  } catch (error) {
    console.error('Google registration error:', error);
    res.status(500).json({
      error: 'Google registration failed',
      message: 'An error occurred during Google registration'
    });
  }
});

// Google OAuth login
app.post('/v1/auth/google/login', async (req, res) => {
  try {
    const { id_token, google_id, email } = req.body;

    // Verify Google ID token
    const ticket = await googleClient.verifyIdToken({
      idToken: id_token,
      audience: '************-pps8qgn62lj4osfmh64u8d3armudmtle.apps.googleusercontent.com'
    });

    const payload = ticket.getPayload();
    if (!payload || payload.sub !== google_id) {
      return res.status(401).json({
        error: 'Invalid Google token',
        message: 'Google authentication failed'
      });
    }

    // Find user
    const user = users.find(u => u.email === email || u.providerId === google_id);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'No account found with this Google account'
      });
    }

    // Update last login
    user.lastLoginAt = new Date().toISOString();

    // Generate token
    const token = generateToken(user.id);

    console.log(`✅ Google user logged in: ${email}`);

    res.json({
      message: 'Google login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        firstName: user.firstName,
        lastName: user.lastName,
        profileImage: user.profileImage,
        trustScore: user.trustScore,
        rating: user.rating,
        isVerified: user.isVerified,
        provider: user.provider,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      }
    });
  } catch (error) {
    console.error('Google login error:', error);
    res.status(500).json({
      error: 'Google login failed',
      message: 'An error occurred during Google login'
    });
  }
});

// Get user profile
app.get('/v1/auth/profile', authenticateToken, (req, res) => {
  const user = users.find(u => u.id === req.userId);
  if (!user) {
    return res.status(404).json({
      error: 'User not found',
      message: 'User profile not found'
    });
  }

  res.json({
    id: user.id,
    email: user.email,
    name: user.name,
    firstName: user.firstName,
    lastName: user.lastName,
    phone: user.phone,
    profileImage: user.profileImage,
    trustScore: user.trustScore,
    rating: user.rating,
    isVerified: user.isVerified,
    provider: user.provider,
    createdAt: user.createdAt,
    lastLoginAt: user.lastLoginAt
  });
});

// Get road issues
app.get('/v1/issues', (req, res) => {
  res.json({
    issues: issues.slice(0, 50), // Return first 50 issues
    pagination: {
      limit: 50,
      offset: 0,
      total: issues.length
    }
  });
});

// Create road issue
app.post('/v1/issues', authenticateToken, (req, res) => {
  const {
    type,
    location_name,
    latitude,
    longitude,
    severity,
    description,
    image_url
  } = req.body;

  const issue = {
    id: generateId(),
    type,
    location_name,
    latitude,
    longitude,
    severity,
    description,
    primary_image_url: image_url,
    reported_by: req.userId,
    status: 'open',
    verified: false,
    verification_count: 0,
    created_at: new Date().toISOString()
  };

  issues.push(issue);

  console.log(`✅ Issue reported: ${type} at ${location_name}`);

  res.status(201).json({
    message: 'Issue created successfully',
    issue
  });
});

// Get active SOS alerts
app.get('/v1/sos/active', (req, res) => {
  const activeAlerts = sosAlerts.filter(alert => alert.status === 'active');
  res.json({ alerts: activeAlerts });
});

// Create SOS alert
app.post('/v1/sos', authenticateToken, (req, res) => {
  const {
    category,
    description,
    latitude,
    longitude,
    location_address
  } = req.body;

  const alert = {
    id: generateId(),
    user_id: req.userId,
    category,
    description,
    latitude,
    longitude,
    location_address,
    status: 'active',
    created_at: new Date().toISOString()
  };

  sosAlerts.push(alert);

  console.log(`🚨 SOS alert created: ${category} at ${location_address || 'Unknown location'}`);

  res.status(201).json({
    message: 'SOS alert created successfully',
    alert
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `The route ${req.originalUrl} does not exist`,
    suggestion: 'Check /api/docs for available endpoints'
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 RoadPulse Backend running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 API docs: http://localhost:${PORT}/api/docs`);
  console.log(`🗄️ Database: In-Memory (for testing)`);
  console.log(`🔐 Authentication: Email + Google OAuth`);
  console.log(`✅ Ready for frontend connections!`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});
