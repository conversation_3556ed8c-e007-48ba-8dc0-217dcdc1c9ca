/**
 * Validation Middleware
 * Request validation and sanitization
 */

const { body, param, query, validationResult } = require('express-validator');
const { handleValidationError } = require('./errorHandler');

/**
 * Handle validation results
 */
const handleValidation = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }
  next();
};

/**
 * Common validation rules
 */
const validationRules = {
  // User validation
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  
  password: body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  name: body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be 2-100 characters'),
  
  phone: body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Valid phone number required'),
  
  // Location validation
  latitude: body('latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  
  longitude: body('longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),
  
  // Issue validation
  issueType: body('type')
    .isIn(['pothole', 'construction', 'accident', 'traffic', 'police', 'camera', 'roadblock'])
    .withMessage('Invalid issue type'),
  
  severity: body('severity')
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid severity level'),
  
  description: body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description must be 10-1000 characters'),
  
  // SOS validation
  sosCategory: body('category')
    .isIn(['medical', 'mechanical', 'security', 'accident', 'other'])
    .withMessage('Invalid SOS category'),
  
  // Query validation
  limitQuery: query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  offsetQuery: query('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Offset must be 0 or greater'),
  
  radiusQuery: query('radius')
    .optional()
    .isFloat({ min: 0.1, max: 50 })
    .withMessage('Radius must be between 0.1 and 50 km'),
  
  // UUID validation
  uuidParam: param('id')
    .isUUID()
    .withMessage('Invalid ID format')
};

/**
 * Validation rule sets for different endpoints
 */
const validationSets = {
  // Authentication
  register: [
    validationRules.email,
    validationRules.password,
    validationRules.name,
    validationRules.phone,
    handleValidation
  ],
  
  login: [
    validationRules.email,
    body('password').notEmpty().withMessage('Password is required'),
    handleValidation
  ],
  
  // Issues
  createIssue: [
    validationRules.issueType,
    body('location_name').trim().isLength({ min: 3, max: 255 }).withMessage('Location name must be 3-255 characters'),
    validationRules.latitude,
    validationRules.longitude,
    validationRules.severity,
    validationRules.description,
    body('image_url').optional().isURL().withMessage('Invalid image URL'),
    handleValidation
  ],
  
  verifyIssue: [
    validationRules.uuidParam,
    body('verification').isIn(['confirm', 'dispute']).withMessage('Verification must be confirm or dispute'),
    body('notes').optional().trim().isLength({ max: 500 }).withMessage('Notes must be 500 characters or less'),
    handleValidation
  ],
  
  // SOS
  createSOS: [
    validationRules.sosCategory,
    validationRules.description,
    validationRules.latitude,
    validationRules.longitude,
    body('location_address').optional().trim().isLength({ max: 255 }).withMessage('Location address too long'),
    handleValidation
  ],
  
  // Location
  updateLocation: [
    validationRules.latitude,
    validationRules.longitude,
    body('accuracy').optional().isFloat({ min: 0 }).withMessage('Accuracy must be positive'),
    body('heading').optional().isFloat({ min: 0, max: 360 }).withMessage('Heading must be 0-360 degrees'),
    body('speed').optional().isFloat({ min: 0 }).withMessage('Speed must be positive'),
    handleValidation
  ],
  
  // Query validation
  listWithPagination: [
    validationRules.limitQuery,
    validationRules.offsetQuery,
    handleValidation
  ],
  
  locationQuery: [
    query('lat').optional().isFloat({ min: -90, max: 90 }).withMessage('Invalid latitude'),
    query('lng').optional().isFloat({ min: -180, max: 180 }).withMessage('Invalid longitude'),
    validationRules.radiusQuery,
    handleValidation
  ]
};

module.exports = {
  validationRules,
  validationSets,
  handleValidation
};
